
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, Users, Activity, Clock, Loader2, AlertCircle } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { useToast } from '@/hooks/use-toast';
import { adminService, type SystemStats, type Appointment } from '@/services';

interface StatCardProps {
  title: string;
  value: string | number;
  description: string;
  icon: React.ReactNode;
  loading?: boolean;
}

const StatCard = ({ title, value, description, icon, loading = false }: StatCardProps) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {loading ? (
            <Loader2 className="h-6 w-6 animate-spin" />
          ) : (
            value
          )}
        </div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );
};

const AppointmentItem = ({ appointment }: { appointment: Appointment }) => {
  const appointmentDate = new Date(appointment.appointmentDate);
  const isToday = appointmentDate.toDateString() === new Date().toDateString();
  const isTomorrow = appointmentDate.toDateString() === new Date(Date.now() + 86400000).toDateString();

  let timeDisplay = appointmentDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  if (isToday) {
    timeDisplay = `Today, ${timeDisplay}`;
  } else if (isTomorrow) {
    timeDisplay = `Tomorrow, ${timeDisplay}`;
  } else {
    timeDisplay = `${appointmentDate.toLocaleDateString()}, ${timeDisplay}`;
  }

  return (
    <div className="flex items-center justify-between rounded-lg border p-3">
      <div className="space-y-1">
        <p className="text-sm font-medium leading-none">
          {appointment.patient?.user?.name || 'Unknown Patient'}
        </p>
        <p className="text-sm text-muted-foreground">
          {appointment.consultationType === 'VIDEO' ? 'Video Consultation' : 'Audio Consultation'}
        </p>
      </div>
      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm text-muted-foreground">{timeDisplay}</span>
      </div>
    </div>
  );
};

const AdminDashboard = () => {
  const isMobile = useIsMobile();
  const { toast } = useToast();

  // State for data
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [upcomingAppointments, setUpcomingAppointments] = useState<Appointment[]>([]);

  // Loading states
  const [statsLoading, setStatsLoading] = useState(true);
  const [appointmentsLoading, setAppointmentsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load system statistics
  const loadStats = async () => {
    try {
      setStatsLoading(true);
      setError(null);
      const response = await adminService.getSystemStats();

      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (error: any) {
      console.error('Error loading system stats:', error);
      setError('Failed to load system statistics');
      toast({
        title: 'Error Loading Statistics',
        description: error.message || 'Failed to load system statistics',
        variant: 'destructive',
      });
    } finally {
      setStatsLoading(false);
    }
  };

  // Load upcoming appointments
  const loadUpcomingAppointments = async () => {
    try {
      setAppointmentsLoading(true);
      const today = new Date();
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

      const response = await adminService.getAppointments({
        status: 'SCHEDULED',
        startDate: today.toISOString(),
        endDate: nextWeek.toISOString(),
        limit: 10,
        sortBy: 'appointmentDate',
        sortOrder: 'asc'
      });

      if (response.success && response.data?.appointments) {
        setUpcomingAppointments(response.data.appointments);
      }
    } catch (error: any) {
      console.error('Error loading upcoming appointments:', error);
      toast({
        title: 'Error Loading Appointments',
        description: error.message || 'Failed to load upcoming appointments',
        variant: 'destructive',
      });
    } finally {
      setAppointmentsLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadStats();
    loadUpcomingAppointments();
  }, []);

  // Show error state if there's an error
  if (error && !stats && !upcomingAppointments.length) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-sm text-muted-foreground">
            {new Date().toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </p>
        </div>

        <Card>
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Error Loading Dashboard</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => {
                loadStats();
                loadUpcomingAppointments();
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Retry
            </button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Prepare stats data for display
  const statsData = [
    {
      title: "Total Appointments",
      value: stats?.appointments?.total || 0,
      description: "All appointments in system",
      icon: <Calendar className="h-4 w-4 text-muted-foreground" />,
      loading: statsLoading
    },
    {
      title: "Total Users",
      value: stats?.users?.total || 0,
      description: `${stats?.users?.patients || 0} patients, ${stats?.users?.providers || 0} providers`,
      icon: <Users className="h-4 w-4 text-muted-foreground" />,
      loading: statsLoading
    },
    {
      title: "Completed Consultations",
      value: stats?.consultations?.total || 0,
      description: `Avg duration: ${stats?.consultations?.averageDuration || 0} min`,
      icon: <Activity className="h-4 w-4 text-muted-foreground" />,
      loading: statsLoading
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold tracking-tight">Admin Dashboard</h1>
        <p className="text-sm text-muted-foreground">
          {new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}
        </p>
      </div>

      <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-3'}`}>
        {statsData.map((stat, index) => (
          <StatCard
            key={index}
            title={stat.title}
            value={stat.value}
            description={stat.description}
            icon={stat.icon}
            loading={stat.loading}
          />
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Upcoming Appointments</CardTitle>
          <CardDescription>
            {appointmentsLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Loading appointments...
              </div>
            ) : (
              `${upcomingAppointments.length} consultations scheduled`
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {appointmentsLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="flex items-center gap-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Loading appointments...</span>
              </div>
            </div>
          ) : upcomingAppointments.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No upcoming appointments</h3>
              <p className="text-gray-600">All appointments are up to date</p>
            </div>
          ) : (
            <div className="space-y-3">
              {upcomingAppointments.map((appointment) => (
                <AppointmentItem
                  key={appointment.id}
                  appointment={appointment}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminDashboard;
