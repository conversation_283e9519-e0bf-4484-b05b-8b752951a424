import { apiClient, ApiResponse } from './api';
import { User, Patient, Provider } from './userService';
import { Appointment } from './appointmentApiService';

// Admin dashboard interfaces
export interface SystemStats {
  users: {
    total: number;
    patients: number;
    providers: number;
    newUsers: number;
  };
  appointments: {
    total: number;
    scheduled: number;
    completed: number;
    cancelled: number;
  };
  consultations: {
    total: number;
    audio: number;
    video: number;
    averageDuration: number;
  };
  payments: {
    total: number;
    currency: string;
  };
}

export interface DashboardData {
  stats: SystemStats;
  recentAppointments: Appointment[];
  recentUsers: User[];
  systemHealth: {
    status: 'healthy' | 'warning' | 'error';
    uptime: number;
    lastBackup: string;
  };
}

export interface AdminUserListParams {
  role?: 'PATIENT' | 'PROVIDER' | 'ADMIN';
  status?: 'active' | 'inactive' | 'suspended';
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: 'name' | 'email' | 'createdAt' | 'lastLogin';
  sortOrder?: 'asc' | 'desc';
}

export interface AdminUserListResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
}

export interface AdminAppointmentListParams {
  status?: string;
  patientId?: string;
  providerId?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
  sortBy?: 'appointmentDate' | 'createdAt' | 'status';
  sortOrder?: 'asc' | 'desc';
}

export interface AdminAppointmentListResponse {
  appointments: Appointment[];
  total: number;
  page: number;
  limit: number;
}

export interface CreateUserRequest {
  email: string;
  password: string;
  name: string;
  role: 'PATIENT' | 'PROVIDER' | 'ADMIN';
  phone?: string;
  additionalInfo?: any;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  phone?: string;
  role?: 'PATIENT' | 'PROVIDER' | 'ADMIN';
  status?: 'active' | 'inactive' | 'suspended';
  additionalInfo?: any;
}

export interface SystemSettings {
  siteName: string;
  maintenanceMode: boolean;
  registrationEnabled: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  maxAppointmentsPerDay: number;
  appointmentDuration: number;
  timezone: string;
}

class AdminService {
  // Get dashboard data
  async getDashboardData(): Promise<ApiResponse<DashboardData>> {
    return apiClient.get<DashboardData>('/admin/dashboard');
  }

  // Get system statistics
  async getSystemStats(period?: 'day' | 'week' | 'month' | 'year'): Promise<ApiResponse<SystemStats>> {
    const url = `/admin/stats${period ? `?period=${period}` : ''}`;
    return apiClient.get<SystemStats>(url);
  }

  // User Management
  async getUsers(params?: AdminUserListParams): Promise<ApiResponse<AdminUserListResponse>> {
    const queryParams = new URLSearchParams();
    
    if (params?.role) queryParams.append('role', params.role);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const url = `/admin/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get<AdminUserListResponse>(url);
  }

  async getUserById(id: string): Promise<ApiResponse<User>> {
    return apiClient.get<User>(`/admin/users/${id}`);
  }

  async createUser(data: CreateUserRequest): Promise<ApiResponse<User>> {
    return apiClient.post<User>('/admin/users', data);
  }

  async updateUser(id: string, data: UpdateUserRequest): Promise<ApiResponse<User>> {
    return apiClient.put<User>(`/admin/users/${id}`, data);
  }

  async deleteUser(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/admin/users/${id}`);
  }

  async suspendUser(id: string, reason?: string): Promise<ApiResponse<User>> {
    return apiClient.patch<User>(`/admin/users/${id}/suspend`, { reason });
  }

  async activateUser(id: string): Promise<ApiResponse<User>> {
    return apiClient.patch<User>(`/admin/users/${id}/activate`);
  }

  // Appointment Management
  async getAppointments(params?: AdminAppointmentListParams): Promise<ApiResponse<AdminAppointmentListResponse>> {
    const queryParams = new URLSearchParams();
    
    if (params?.status) queryParams.append('status', params.status);
    if (params?.patientId) queryParams.append('patientId', params.patientId);
    if (params?.providerId) queryParams.append('providerId', params.providerId);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const url = `/admin/appointments${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get<AdminAppointmentListResponse>(url);
  }

  async getAppointmentById(id: string): Promise<ApiResponse<Appointment>> {
    return apiClient.get<Appointment>(`/admin/appointments/${id}`);
  }

  async updateAppointment(id: string, data: any): Promise<ApiResponse<Appointment>> {
    return apiClient.put<Appointment>(`/admin/appointments/${id}`, data);
  }

  async cancelAppointment(id: string, reason?: string): Promise<ApiResponse<Appointment>> {
    return apiClient.patch<Appointment>(`/admin/appointments/${id}/cancel`, { reason });
  }

  // System Settings
  async getSystemSettings(): Promise<ApiResponse<SystemSettings>> {
    return apiClient.get<SystemSettings>('/admin/settings');
  }

  async updateSystemSettings(settings: Partial<SystemSettings>): Promise<ApiResponse<SystemSettings>> {
    return apiClient.put<SystemSettings>('/admin/settings', settings);
  }

  // Reports and Analytics
  async generateReport(type: 'users' | 'appointments' | 'revenue', params?: any): Promise<ApiResponse<any>> {
    return apiClient.post(`/admin/reports/${type}`, params);
  }

  async exportData(type: 'users' | 'appointments' | 'all', format: 'csv' | 'json' = 'csv'): Promise<ApiResponse<{ downloadUrl: string }>> {
    return apiClient.post(`/admin/export/${type}`, { format });
  }

  // System Health
  async getSystemHealth(): Promise<ApiResponse<any>> {
    return apiClient.get('/admin/health');
  }

  async performBackup(): Promise<ApiResponse<{ backupId: string; status: string }>> {
    return apiClient.post('/admin/backup');
  }

  // Audit Logs
  async getAuditLogs(params?: { page?: number; limit?: number; userId?: string; action?: string }): Promise<ApiResponse<any>> {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.userId) queryParams.append('userId', params.userId);
    if (params?.action) queryParams.append('action', params.action);

    const url = `/admin/audit-logs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get(url);
  }
}

export const adminService = new AdminService();
export default adminService;
