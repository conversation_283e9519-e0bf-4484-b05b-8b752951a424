import { apiClient, ApiResponse } from './api';
import { User, Patient, Provider } from './userService';

// Appointment interfaces
export interface Appointment {
  id: string;
  patientId: string;
  providerId: string;
  appointmentDate: string;
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'NO_SHOW';
  consultationType: 'VIDEO' | 'AUDIO';
  reason?: string;
  notes?: string;
  patient?: Patient & { user: User };
  provider?: Provider & { user: User };
  consultation?: Consultation;
  createdAt: string;
  updatedAt: string;
}

export interface Consultation {
  id: string;
  appointmentId: string;
  roomId: string;
  status: 'WAITING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  startTime?: string;
  endTime?: string;
  duration?: number;
  recordingUrl?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateAppointmentRequest {
  patientId: string;
  providerId: string;
  appointmentDate: string;
  consultationType: 'VIDEO' | 'AUDIO';
  reason?: string;
}

export interface UpdateAppointmentRequest {
  appointmentDate?: string;
  status?: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'NO_SHOW';
  consultationType?: 'VIDEO' | 'AUDIO';
  reason?: string;
  notes?: string;
}

export interface AppointmentListParams {
  status?: string;
  patientId?: string;
  providerId?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

export interface AppointmentListResponse {
  appointments: Appointment[];
  total: number;
  page: number;
  limit: number;
}

export interface TimeSlot {
  startTime: string;
  endTime: string;
  available: boolean;
}

export interface AvailabilityResponse {
  date: string;
  slots: TimeSlot[];
}

class AppointmentApiService {
  // Get appointments for current user
  async getMyAppointments(params?: AppointmentListParams): Promise<ApiResponse<AppointmentListResponse>> {
    const queryParams = new URLSearchParams();
    
    if (params?.status) queryParams.append('status', params.status);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const url = `/appointments/my${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get<AppointmentListResponse>(url);
  }

  // Get all appointments (admin/provider)
  async getAppointments(params?: AppointmentListParams): Promise<ApiResponse<AppointmentListResponse>> {
    const queryParams = new URLSearchParams();
    
    if (params?.status) queryParams.append('status', params.status);
    if (params?.patientId) queryParams.append('patientId', params.patientId);
    if (params?.providerId) queryParams.append('providerId', params.providerId);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const url = `/appointments${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get<AppointmentListResponse>(url);
  }

  // Get appointment by ID
  async getAppointmentById(id: string): Promise<ApiResponse<Appointment>> {
    return apiClient.get<Appointment>(`/appointments/${id}`);
  }

  // Create new appointment
  async createAppointment(data: CreateAppointmentRequest): Promise<ApiResponse<Appointment>> {
    return apiClient.post<Appointment>('/appointments', data);
  }

  // Update appointment
  async updateAppointment(id: string, data: UpdateAppointmentRequest): Promise<ApiResponse<Appointment>> {
    return apiClient.put<Appointment>(`/appointments/${id}`, data);
  }

  // Cancel appointment
  async cancelAppointment(id: string, reason?: string): Promise<ApiResponse<Appointment>> {
    return apiClient.patch<Appointment>(`/appointments/${id}/cancel`, { reason });
  }

  // Reschedule appointment
  async rescheduleAppointment(id: string, newDate: string): Promise<ApiResponse<Appointment>> {
    return apiClient.patch<Appointment>(`/appointments/${id}/reschedule`, { appointmentDate: newDate });
  }

  // Get provider availability
  async getProviderAvailability(providerId: string, date: string): Promise<ApiResponse<AvailabilityResponse>> {
    return apiClient.get<AvailabilityResponse>(`/appointments/availability/${providerId}?date=${date}`);
  }

  // Join consultation (creates Daily.co room if needed)
  async joinConsultation(appointmentId: string): Promise<ApiResponse<Consultation & { roomUrl: string; token: string }>> {
    return apiClient.post<Consultation & { roomUrl: string; token: string }>(`/appointments/${appointmentId}/join`);
  }

  // End consultation
  async endConsultation(appointmentId: string, notes?: string): Promise<ApiResponse<Consultation>> {
    return apiClient.post<Consultation>(`/appointments/${appointmentId}/end`, { notes });
  }

  // Get consultation details
  async getConsultation(appointmentId: string): Promise<ApiResponse<Consultation>> {
    return apiClient.get<Consultation>(`/appointments/${appointmentId}/consultation`);
  }

  // Start consultation (for providers)
  async startConsultation(appointmentId: string): Promise<ApiResponse<Consultation & { roomUrl: string; token: string }>> {
    return apiClient.post<Consultation & { roomUrl: string; token: string }>(`/appointments/${appointmentId}/start`);
  }

  // Get Daily.co room token for existing consultation
  async getConsultationToken(appointmentId: string): Promise<ApiResponse<{ token: string; roomUrl: string }>> {
    return apiClient.get<{ token: string; roomUrl: string }>(`/appointments/${appointmentId}/token`);
  }

  // Get upcoming appointments (next 7 days)
  async getUpcomingAppointments(): Promise<ApiResponse<Appointment[]>> {
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 7);
    
    const params = {
      status: 'SCHEDULED',
      startDate: new Date().toISOString(),
      endDate: endDate.toISOString(),
    };

    const response = await this.getMyAppointments(params);
    return {
      ...response,
      data: response.data?.appointments || [],
    };
  }

  // Get today's appointments
  async getTodaysAppointments(): Promise<ApiResponse<Appointment[]>> {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const params = {
      status: 'SCHEDULED',
      startDate: today.toISOString().split('T')[0] + 'T00:00:00.000Z',
      endDate: tomorrow.toISOString().split('T')[0] + 'T00:00:00.000Z',
    };

    const response = await this.getMyAppointments(params);
    return {
      ...response,
      data: response.data?.appointments || [],
    };
  }

  // Get appointment history
  async getAppointmentHistory(limit: number = 10): Promise<ApiResponse<Appointment[]>> {
    const params = {
      status: 'COMPLETED',
      limit,
      page: 1,
    };

    const response = await this.getMyAppointments(params);
    return {
      ...response,
      data: response.data?.appointments || [],
    };
  }
}

export const appointmentApiService = new AppointmentApiService();
export default appointmentApiService;
