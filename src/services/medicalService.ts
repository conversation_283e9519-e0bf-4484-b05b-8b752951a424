import { apiClient, ApiResponse } from './api';
import { User, Patient, Provider } from './userService';

// Medical record interfaces
export interface MedicalRecord {
  id: string;
  patientId: string;
  providerId: string;
  appointmentId?: string;
  type: 'consultation' | 'lab_result' | 'prescription' | 'diagnosis' | 'treatment_plan' | 'other';
  title: string;
  content: string;
  attachments?: string[];
  tags?: string[];
  isPrivate: boolean;
  patient?: Patient & { user: User };
  provider?: Provider & { user: User };
  createdAt: string;
  updatedAt: string;
}

export interface Prescription {
  id: string;
  patientId: string;
  providerId: string;
  appointmentId?: string;
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  instructions: string;
  status: 'active' | 'completed' | 'cancelled' | 'expired';
  startDate: string;
  endDate?: string;
  refillsRemaining?: number;
  patient?: Patient & { user: User };
  provider?: Provider & { user: User };
  createdAt: string;
  updatedAt: string;
}

export interface CreateMedicalRecordRequest {
  patientId: string;
  appointmentId?: string;
  type: 'consultation' | 'lab_result' | 'prescription' | 'diagnosis' | 'treatment_plan' | 'other';
  title: string;
  content: string;
  attachments?: string[];
  tags?: string[];
  isPrivate?: boolean;
}

export interface UpdateMedicalRecordRequest {
  type?: 'consultation' | 'lab_result' | 'prescription' | 'diagnosis' | 'treatment_plan' | 'other';
  title?: string;
  content?: string;
  attachments?: string[];
  tags?: string[];
  isPrivate?: boolean;
}

export interface CreatePrescriptionRequest {
  patientId: string;
  appointmentId?: string;
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  instructions: string;
  startDate: string;
  endDate?: string;
  refillsRemaining?: number;
}

export interface UpdatePrescriptionRequest {
  medication?: string;
  dosage?: string;
  frequency?: string;
  duration?: string;
  instructions?: string;
  status?: 'active' | 'completed' | 'cancelled' | 'expired';
  startDate?: string;
  endDate?: string;
  refillsRemaining?: number;
}

export interface MedicalRecordListParams {
  patientId?: string;
  providerId?: string;
  type?: string;
  startDate?: string;
  endDate?: string;
  tags?: string[];
  page?: number;
  limit?: number;
}

export interface PrescriptionListParams {
  patientId?: string;
  providerId?: string;
  status?: 'active' | 'completed' | 'cancelled' | 'expired';
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

export interface MedicalRecordListResponse {
  records: MedicalRecord[];
  total: number;
  page: number;
  limit: number;
}

export interface PrescriptionListResponse {
  prescriptions: Prescription[];
  total: number;
  page: number;
  limit: number;
}

class MedicalService {
  // Medical Records
  async getMedicalRecords(params?: MedicalRecordListParams): Promise<ApiResponse<MedicalRecordListResponse>> {
    const queryParams = new URLSearchParams();
    
    if (params?.patientId) queryParams.append('patientId', params.patientId);
    if (params?.providerId) queryParams.append('providerId', params.providerId);
    if (params?.type) queryParams.append('type', params.type);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.tags) params.tags.forEach(tag => queryParams.append('tags', tag));
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const url = `/medical-records${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get<MedicalRecordListResponse>(url);
  }

  async getMyMedicalRecords(params?: Omit<MedicalRecordListParams, 'patientId'>): Promise<ApiResponse<MedicalRecordListResponse>> {
    const queryParams = new URLSearchParams();
    
    if (params?.providerId) queryParams.append('providerId', params.providerId);
    if (params?.type) queryParams.append('type', params.type);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.tags) params.tags.forEach(tag => queryParams.append('tags', tag));
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const url = `/medical-records/my${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get<MedicalRecordListResponse>(url);
  }

  async getMedicalRecordById(id: string): Promise<ApiResponse<MedicalRecord>> {
    return apiClient.get<MedicalRecord>(`/medical-records/${id}`);
  }

  async createMedicalRecord(data: CreateMedicalRecordRequest): Promise<ApiResponse<MedicalRecord>> {
    return apiClient.post<MedicalRecord>('/medical-records', data);
  }

  async updateMedicalRecord(id: string, data: UpdateMedicalRecordRequest): Promise<ApiResponse<MedicalRecord>> {
    return apiClient.put<MedicalRecord>(`/medical-records/${id}`, data);
  }

  async deleteMedicalRecord(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/medical-records/${id}`);
  }

  // Prescriptions
  async getPrescriptions(params?: PrescriptionListParams): Promise<ApiResponse<PrescriptionListResponse>> {
    const queryParams = new URLSearchParams();
    
    if (params?.patientId) queryParams.append('patientId', params.patientId);
    if (params?.providerId) queryParams.append('providerId', params.providerId);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const url = `/prescriptions${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get<PrescriptionListResponse>(url);
  }

  async getMyPrescriptions(params?: Omit<PrescriptionListParams, 'patientId'>): Promise<ApiResponse<PrescriptionListResponse>> {
    const queryParams = new URLSearchParams();
    
    if (params?.providerId) queryParams.append('providerId', params.providerId);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const url = `/prescriptions/my${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get<PrescriptionListResponse>(url);
  }

  async getPrescriptionById(id: string): Promise<ApiResponse<Prescription>> {
    return apiClient.get<Prescription>(`/prescriptions/${id}`);
  }

  async createPrescription(data: CreatePrescriptionRequest): Promise<ApiResponse<Prescription>> {
    return apiClient.post<Prescription>('/prescriptions', data);
  }

  async updatePrescription(id: string, data: UpdatePrescriptionRequest): Promise<ApiResponse<Prescription>> {
    return apiClient.put<Prescription>(`/prescriptions/${id}`, data);
  }

  async deletePrescription(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/prescriptions/${id}`);
  }

  // Helper methods for common use cases
  async getActivePrescriptions(): Promise<ApiResponse<Prescription[]>> {
    const response = await this.getMyPrescriptions({ status: 'active' });
    return {
      ...response,
      data: response.data?.prescriptions || [],
    };
  }

  async getRecentMedicalRecords(limit: number = 10): Promise<ApiResponse<MedicalRecord[]>> {
    const response = await this.getMyMedicalRecords({ limit, page: 1 });
    return {
      ...response,
      data: response.data?.records || [],
    };
  }
}

export const medicalService = new MedicalService();
export default medicalService;
