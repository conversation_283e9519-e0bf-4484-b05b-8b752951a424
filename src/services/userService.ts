import { apiClient, ApiResponse } from './api';

// User interfaces
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'PATIENT' | 'PROVIDER' | 'ADMIN';
  phone?: string;
  emailVerified?: string;
  patient?: Patient;
  provider?: Provider;
  createdAt: string;
  updatedAt: string;
}

export interface Patient {
  id: string;
  userId: string;
  dateOfBirth?: string;
  address?: string;
  emergencyContact?: string;
  medicalHistory?: any;
  insurance?: any;
  user?: User;
}

export interface Provider {
  id: string;
  userId: string;
  specialization?: string;
  licenseNumber?: string;
  availability?: any;
  user?: User;
}

export interface CreateUserRequest {
  email: string;
  password: string;
  name: string;
  role: 'PATIENT' | 'PROVIDER' | 'ADMIN';
  phone?: string;
  additionalInfo?: any;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  phone?: string;
  additionalInfo?: any;
}

export interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
}

export interface UserListParams {
  role?: 'PATIENT' | 'PROVIDER' | 'ADMIN';
  page?: number;
  limit?: number;
  search?: string;
}

class UserService {
  // Get current user profile
  async getCurrentUser(): Promise<ApiResponse<User>> {
    return apiClient.get<User>('/users/me');
  }

  // Get user by ID
  async getUserById(id: string): Promise<ApiResponse<User>> {
    return apiClient.get<User>(`/users/${id}`);
  }

  // Update current user profile
  async updateCurrentUser(data: UpdateUserRequest): Promise<ApiResponse<User>> {
    return apiClient.put<User>('/users/me', data);
  }

  // Update user by ID (admin only)
  async updateUser(id: string, data: UpdateUserRequest): Promise<ApiResponse<User>> {
    return apiClient.put<User>(`/users/${id}`, data);
  }

  // Delete user (admin only)
  async deleteUser(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/users/${id}`);
  }

  // Get all users (admin only)
  async getUsers(params?: UserListParams): Promise<ApiResponse<UserListResponse>> {
    const queryParams = new URLSearchParams();
    
    if (params?.role) queryParams.append('role', params.role);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);

    const url = `/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get<UserListResponse>(url);
  }

  // Create new user (admin only)
  async createUser(data: CreateUserRequest): Promise<ApiResponse<User>> {
    return apiClient.post<User>('/users', data);
  }

  // Get patient profile
  async getPatientProfile(patientId?: string): Promise<ApiResponse<Patient>> {
    const url = patientId ? `/patients/${patientId}` : '/patients/me';
    return apiClient.get<Patient>(url);
  }

  // Update patient profile
  async updatePatientProfile(data: Partial<Patient>, patientId?: string): Promise<ApiResponse<Patient>> {
    const url = patientId ? `/patients/${patientId}` : '/patients/me';
    return apiClient.put<Patient>(url, data);
  }

  // Get provider profile
  async getProviderProfile(providerId?: string): Promise<ApiResponse<Provider>> {
    const url = providerId ? `/providers/${providerId}` : '/providers/me';
    return apiClient.get<Provider>(url);
  }

  // Update provider profile
  async updateProviderProfile(data: Partial<Provider>, providerId?: string): Promise<ApiResponse<Provider>> {
    const url = providerId ? `/providers/${providerId}` : '/providers/me';
    return apiClient.put<Provider>(url, data);
  }

  // Get all patients (provider/admin only)
  async getPatients(params?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse<{ patients: Patient[]; total: number; page: number; limit: number }>> {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);

    const url = `/patients${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get(url);
  }

  // Get all providers (admin only)
  async getProviders(params?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse<{ providers: Provider[]; total: number; page: number; limit: number }>> {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);

    const url = `/providers${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get(url);
  }
}

export const userService = new UserService();
export default userService;
