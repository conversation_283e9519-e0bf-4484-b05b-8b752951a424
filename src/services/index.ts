// Export API client
export { apiClient, ApiClient } from './api';
export type { ApiResponse, ApiError } from './api';

// Export auth service (existing)
export { default as authService } from './authService';

// Export user service
export { userService } from './userService';
export type {
  User,
  Patient,
  Provider,
  CreateUserRequest,
  UpdateUserRequest,
  UserListResponse,
  UserListParams,
} from './userService';

// Export appointment service
export { appointmentApiService } from './appointmentApiService';
export type {
  Appointment,
  Consultation,
  CreateAppointmentRequest,
  UpdateAppointmentRequest,
  AppointmentListParams,
  AppointmentListResponse,
  TimeSlot,
  AvailabilityResponse,
} from './appointmentApiService';

// Export admin service
export { adminService } from './adminService';
export type {
  SystemStats,
  DashboardData,
  AdminUserListParams,
  AdminUserListResponse,
  AdminAppointmentListParams,
  AdminAppointmentListResponse,
  SystemSettings,
} from './adminService';

// Export medical service
export { medicalService } from './medicalService';
export type {
  MedicalRecord,
  Prescription,
  CreateMedicalRecordRequest,
  UpdateMedicalRecordRequest,
  CreatePrescriptionRequest,
  UpdatePrescriptionRequest,
  MedicalRecordListParams,
  PrescriptionListParams,
  MedicalRecordListResponse,
  PrescriptionListResponse,
} from './medicalService';

// Re-export existing services for compatibility (using named exports)
export * from './audioCallService';
export * from './calendarIntegrationService';
export * from './calendarService';
export * from './notificationService';
export * from './videoCallService';
export * from './webrtcService';

// Service instances for easy access
export const services = {
  auth: authService,
  user: userService,
  appointment: appointmentApiService,
  admin: adminService,
  medical: medicalService,
};

export default services;
