import axios from 'axios';

const API_URL = import.meta.env.VITE_API_BASE_URL + '/auth/';
console.log('API_URL in authService:', API_URL);

// Types for API responses
interface AuthResponse {
  success: boolean;
  message: string;
  data: {
    user: {
      id: string;
      email: string;
      name: string;
      role: string;
      phone?: string;
      emailVerified?: string;
      patient?: any;
      provider?: any;
      createdAt: string;
      updatedAt: string;
    };
    accessToken: string;
    refreshToken: string;
    expiresIn: string;
  };
}

interface AuthError {
  success: false;
  error: {
    code: string;
    message: string;
  };
  errors?: any[];
}

const register = async (name: string, email: string, password: string, role: string = 'PATIENT') => {
  try {
    const response = await axios.post<AuthResponse>(API_URL + 'register', {
      name,
      email,
      password,
      role,
    });

    if (response.data.success && response.data.data) {
      // Store user data and tokens
      localStorage.setItem('user', JSON.stringify(response.data.data.user));
      localStorage.setItem('accessToken', response.data.data.accessToken);
      localStorage.setItem('refreshToken', response.data.data.refreshToken);

      return response.data;
    } else {
      throw new Error('Registration failed');
    }
  } catch (error: any) {
    if (error.response?.data) {
      throw new Error(error.response.data.message || error.response.data.error?.message || 'Registration failed');
    }
    throw error;
  }
};

const login = async (email: string, password: string) => {
  try {
    const response = await axios.post<AuthResponse>(API_URL + 'login', {
      email,
      password,
    });

    if (response.data.success && response.data.data) {
      // Store user data and tokens
      localStorage.setItem('user', JSON.stringify(response.data.data.user));
      localStorage.setItem('accessToken', response.data.data.accessToken);
      localStorage.setItem('refreshToken', response.data.data.refreshToken);

      return response.data;
    } else {
      throw new Error('Login failed');
    }
  } catch (error: any) {
    if (error.response?.data) {
      throw new Error(error.response.data.message || error.response.data.error?.message || 'Login failed');
    }
    throw error;
  }
};

const logout = () => {
  localStorage.removeItem('user');
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
};

const getCurrentUser = () => {
  const userStr = localStorage.getItem('user');
  if (userStr) {
    try {
      return JSON.parse(userStr);
    } catch (error) {
      console.error('Error parsing user data:', error);
      logout(); // Clear corrupted data
      return null;
    }
  }
  return null;
};

const getToken = () => {
  return localStorage.getItem('accessToken');
};

const getRefreshToken = () => {
  return localStorage.getItem('refreshToken');
};

const isAuthenticated = () => {
  const token = getToken();
  const user = getCurrentUser();
  return !!(token && user);
};

// Set up axios interceptor to include auth token in requests
axios.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Set up axios interceptor to handle auth errors
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid, logout user
      logout();
      // Optionally redirect to login page
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

const authService = {
  register,
  login,
  logout,
  getCurrentUser,
  getToken,
  getRefreshToken,
  isAuthenticated,
};

export default authService;
