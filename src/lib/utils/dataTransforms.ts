import { format, parseISO, isValid } from 'date-fns';

// Date formatting utilities
export const formatters = {
  // Standard date formats
  date: (date: string | Date) => {
    const d = typeof date === 'string' ? parseISO(date) : date;
    return isValid(d) ? format(d, 'MMM d, yyyy') : 'Invalid date';
  },
  
  dateTime: (date: string | Date) => {
    const d = typeof date === 'string' ? parseISO(date) : date;
    return isValid(d) ? format(d, 'MMM d, yyyy h:mm a') : 'Invalid date';
  },
  
  time: (date: string | Date) => {
    const d = typeof date === 'string' ? parseISO(date) : date;
    return isValid(d) ? format(d, 'h:mm a') : 'Invalid time';
  },
  
  relativeDate: (date: string | Date) => {
    const d = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(d)) return 'Invalid date';
    
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today.getTime() + 86400000);
    const yesterday = new Date(today.getTime() - 86400000);
    const targetDate = new Date(d.getFullYear(), d.getMonth(), d.getDate());
    
    if (targetDate.getTime() === today.getTime()) {
      return `Today, ${format(d, 'h:mm a')}`;
    } else if (targetDate.getTime() === tomorrow.getTime()) {
      return `Tomorrow, ${format(d, 'h:mm a')}`;
    } else if (targetDate.getTime() === yesterday.getTime()) {
      return `Yesterday, ${format(d, 'h:mm a')}`;
    } else {
      return format(d, 'MMM d, yyyy h:mm a');
    }
  }
};

// Status transformations
export const statusTransforms = {
  appointment: (status: string) => {
    const statusMap: Record<string, { label: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' }> = {
      'SCHEDULED': { label: 'Scheduled', variant: 'default' },
      'CONFIRMED': { label: 'Confirmed', variant: 'default' },
      'IN_PROGRESS': { label: 'In Progress', variant: 'secondary' },
      'COMPLETED': { label: 'Completed', variant: 'secondary' },
      'CANCELLED': { label: 'Cancelled', variant: 'destructive' },
      'NO_SHOW': { label: 'No Show', variant: 'destructive' }
    };
    
    return statusMap[status.toUpperCase()] || { label: status, variant: 'outline' as const };
  },
  
  prescription: (status: string) => {
    const statusMap: Record<string, { label: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' }> = {
      'ACTIVE': { label: 'Active', variant: 'default' },
      'COMPLETED': { label: 'Completed', variant: 'secondary' },
      'CANCELLED': { label: 'Cancelled', variant: 'destructive' },
      'EXPIRED': { label: 'Expired', variant: 'destructive' },
      'DISCONTINUED': { label: 'Discontinued', variant: 'destructive' }
    };
    
    return statusMap[status.toUpperCase()] || { label: status, variant: 'outline' as const };
  },
  
  consultation: (status: string) => {
    const statusMap: Record<string, { label: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' }> = {
      'WAITING': { label: 'Waiting', variant: 'outline' },
      'IN_PROGRESS': { label: 'In Progress', variant: 'default' },
      'COMPLETED': { label: 'Completed', variant: 'secondary' },
      'CANCELLED': { label: 'Cancelled', variant: 'destructive' }
    };
    
    return statusMap[status.toUpperCase()] || { label: status, variant: 'outline' as const };
  }
};

// User role transformations
export const roleTransforms = {
  label: (role: string) => {
    const roleMap: Record<string, string> = {
      'PATIENT': 'Patient',
      'PROVIDER': 'Provider',
      'ADMIN': 'Administrator'
    };
    
    return roleMap[role.toUpperCase()] || role;
  },
  
  color: (role: string) => {
    const colorMap: Record<string, string> = {
      'PATIENT': 'text-blue-600',
      'PROVIDER': 'text-green-600',
      'ADMIN': 'text-purple-600'
    };
    
    return colorMap[role.toUpperCase()] || 'text-gray-600';
  }
};

// Consultation type transformations
export const consultationTypeTransforms = {
  label: (type: string) => {
    const typeMap: Record<string, string> = {
      'VIDEO': 'Video Consultation',
      'AUDIO': 'Audio Consultation'
    };
    
    return typeMap[type.toUpperCase()] || type;
  },
  
  icon: (type: string) => {
    return type.toUpperCase() === 'VIDEO' ? 'Video' : 'Phone';
  }
};

// Currency and number formatting
export const numberTransforms = {
  currency: (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  },
  
  percentage: (value: number, decimals: number = 1) => {
    return `${value.toFixed(decimals)}%`;
  },
  
  duration: (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
    }
  }
};

// Text transformations
export const textTransforms = {
  truncate: (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + '...';
  },
  
  capitalize: (text: string) => {
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  },
  
  initials: (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .join('')
      .substring(0, 2);
  }
};

// Data validation utilities
export const validators = {
  isValidEmail: (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
  
  isValidPhone: (phone: string) => {
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
    return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
  },
  
  isValidDate: (date: string | Date) => {
    const d = typeof date === 'string' ? parseISO(date) : date;
    return isValid(d);
  }
};

// Error message transformations
export const errorTransforms = {
  apiError: (error: any) => {
    if (typeof error === 'string') return error;
    
    if (error?.response?.data?.message) {
      return error.response.data.message;
    }
    
    if (error?.message) {
      return error.message;
    }
    
    if (error?.code === 'NETWORK_ERROR') {
      return 'Network error - please check your connection';
    }
    
    if (error?.status === 401) {
      return 'Authentication required - please log in again';
    }
    
    if (error?.status === 403) {
      return 'You do not have permission to perform this action';
    }
    
    if (error?.status === 404) {
      return 'The requested resource was not found';
    }
    
    if (error?.status >= 500) {
      return 'Server error - please try again later';
    }
    
    return 'An unexpected error occurred';
  },
  
  validationError: (field: string, rule: string) => {
    const messages: Record<string, string> = {
      required: `${field} is required`,
      email: `${field} must be a valid email address`,
      phone: `${field} must be a valid phone number`,
      minLength: `${field} is too short`,
      maxLength: `${field} is too long`,
      pattern: `${field} format is invalid`
    };
    
    return messages[rule] || `${field} is invalid`;
  }
};

// Utility to safely access nested object properties
export const safeGet = <T>(obj: any, path: string, defaultValue: T): T => {
  const keys = path.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current == null || typeof current !== 'object') {
      return defaultValue;
    }
    current = current[key];
  }
  
  return current !== undefined ? current : defaultValue;
};

// Utility to transform API response data
export const transformApiData = <T>(
  data: any,
  transformer: (item: any) => T
): T[] => {
  if (!Array.isArray(data)) {
    return [];
  }
  
  return data.map(transformer).filter(Boolean);
};

export default {
  formatters,
  statusTransforms,
  roleTransforms,
  consultationTypeTransforms,
  numberTransforms,
  textTransforms,
  validators,
  errorTransforms,
  safeGet,
  transformApiData
};
