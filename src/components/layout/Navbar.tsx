import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X, Home, Calendar, Info, Phone, ChevronRight, LogIn, UserPlus, LogOut, LayoutDashboard } from "lucide-react";
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { ThemeToggle } from '../theme/ThemeProvider';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuthStatus } from '@/hooks/use-auth-status';
import authService from '@/services/authService';

const Navbar: React.FC = () => {
  const isMobile = useIsMobile();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { user, isAuthenticated, invalidateAuth } = useAuthStatus();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleLogout = () => {
    authService.logout();
    invalidateAuth();
    navigate('/');
    setIsMenuOpen(false); // Close mobile menu on logout
  };

  // Mobile app-like bottom navigation and top bar
  if (isMobile) {
    return (
      <>
        {/* Mobile top nav bar */}
        <nav className="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-medical-dark-surface border-b border-medical-border-light dark:border-medical-dark-border h-16 flex items-center px-4 shadow-sm">
          <Link to="/" className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-medical-primary to-medical-secondary flex items-center justify-center text-white font-bold text-lg shadow-md">F</div>
            <span className="font-bold text-xl text-medical-primary dark:text-medical-accent">Dr. Fintan</span>
          </Link>
          <div className="flex items-center gap-3 ml-auto">
            <ThemeToggle />
            <Sheet>
              <SheetTrigger asChild>
                <button className="touch-target p-3 rounded-full bg-medical-bg-light dark:bg-medical-dark-surface/50 shadow-sm border border-medical-border-light dark:border-medical-dark-border" aria-label="Menu">
                  <Menu className="h-5 w-5 text-medical-neutral-600 dark:text-medical-dark-text-primary" />
                </button>
              </SheetTrigger>
              <SheetContent side="right" className="bg-white dark:bg-medical-dark-surface border-medical-border-light dark:border-medical-dark-border w-[300px] px-4">
                <div className="flex items-center justify-between mb-6 mt-2 px-2">
                  <div className="flex items-center gap-2">
                    <div className="h-8 w-8 rounded-full bg-gradient-to-br from-medical-primary to-medical-secondary flex items-center justify-center text-white font-bold text-lg">F</div>
                    <span className="font-semibold">Dr. Fintan</span>
                  </div>
                </div>
                <div className="flex flex-col">
                  <Link to="/" className="flex items-center justify-between text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium py-3 px-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                    <div className="flex items-center gap-3">
                      <Home className="h-5 w-5" />
                      Home
                    </div>
                    <ChevronRight className="h-4 w-4 opacity-50" />
                  </Link>
                  <Link to="/about" className="flex items-center justify-between text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium py-3 px-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                    <div className="flex items-center gap-3">
                      <Info className="h-5 w-5" />
                      About
                    </div>
                    <ChevronRight className="h-4 w-4 opacity-50" />
                  </Link>
                  <Link to="/contact" className="flex items-center justify-between text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium py-3 px-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                    <div className="flex items-center gap-3">
                      <Phone className="h-5 w-5" />
                      Contact
                    </div>
                    <ChevronRight className="h-4 w-4 opacity-50" />
                  </Link>
                  {isAuthenticated && user?.role === 'PATIENT' && (
                    <Link to="/patient-dashboard" className="flex items-center justify-between text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium py-3 px-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                      <div className="flex items-center gap-3">
                        <LayoutDashboard className="h-5 w-5" />
                        Dashboard
                      </div>
                      <ChevronRight className="h-4 w-4 opacity-50" />
                    </Link>
                  )}
                  {isAuthenticated && user?.role === 'ADMIN' && (
                    <Link to="/admin/dashboard" className="flex items-center justify-between text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium py-3 px-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                      <div className="flex items-center gap-3">
                        <LayoutDashboard className="h-5 w-5" />
                        Admin Dashboard
                      </div>
                      <ChevronRight className="h-4 w-4 opacity-50" />
                    </Link>
                  )}
                  <div className="border-t dark:border-gray-700 my-4"></div>
                  {isAuthenticated ? (
                    <Button 
                      onClick={handleLogout} 
                      className="w-full bg-red-500 hover:bg-red-600 text-white py-5"
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      Sign Out
                    </Button>
                  ) : (
                    <>
                      <Link to="/auth/login">
                        <Button className="w-full bg-medical-primary hover:bg-medical-primary/90 text-white dark:bg-medical-accent dark:hover:bg-medical-accent/90 py-5 mb-2">
                          <LogIn className="h-4 w-4 mr-2" />
                          Login
                        </Button>
                      </Link>
                      <Link to="/auth/register">
                        <Button className="w-full border-medical-primary text-medical-primary hover:bg-medical-primary hover:text-white dark:border-medical-accent dark:text-medical-accent dark:hover:bg-medical-accent dark:hover:text-white py-5">
                          <UserPlus className="h-4 w-4 mr-2" />
                          Sign Up
                        </Button>
                      </Link>
                    </>
                  )}
                  <Link to="/booking">
                    <Button className="w-full bg-medical-primary hover:bg-medical-primary/90 text-white dark:bg-medical-accent dark:hover:bg-medical-accent/90 py-5 mt-2">
                      Book a Consultation
                    </Button>
                  </Link>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </nav>
        <div className="h-16"></div>
        
        {/* Mobile bottom navigation */}
        <div className="fixed bottom-0 left-0 right-0 h-16 bg-white dark:bg-medical-dark-surface border-t border-medical-border-light dark:border-medical-dark-border flex items-center justify-around px-2 z-50">
          <Link to="/" className="flex flex-col items-center px-1">
            <Home className={`h-6 w-6 ${location.pathname === '/' ? 'text-medical-primary dark:text-medical-accent' : 'text-medical-neutral-500 dark:text-medical-dark-text-secondary'}`} />
            <span className={`text-xs mt-1 ${location.pathname === '/' ? 'text-medical-primary dark:text-medical-accent font-medium' : 'text-medical-neutral-500 dark:text-medical-dark-text-secondary'}`}>Home</span>
          </Link>
          <Link to="/booking" className="flex flex-col items-center">
            <div className="-mt-8 h-14 w-14 rounded-full bg-gradient-to-br from-medical-primary to-medical-secondary flex items-center justify-center shadow-lg">
              <Calendar className="h-7 w-7 text-white" />
            </div>
            <span className={`text-xs -mt-0.5 ${location.pathname === '/booking' ? 'text-medical-primary dark:text-medical-accent font-medium' : 'text-medical-neutral-500 dark:text-medical-dark-text-secondary'}`}>Book</span>
          </Link>
          {isAuthenticated && user?.role === 'PATIENT' && (
            <Link to="/patient-dashboard" className="flex flex-col items-center px-1">
              <LayoutDashboard className={`h-6 w-6 ${location.pathname === '/patient-dashboard' ? 'text-medical-primary dark:text-medical-accent' : 'text-medical-neutral-500 dark:text-medical-dark-text-secondary'}`} />
              <span className={`text-xs mt-1 ${location.pathname === '/patient-dashboard' ? 'text-medical-primary dark:text-medical-accent font-medium' : 'text-medical-neutral-500 dark:text-medical-dark-text-secondary'}`}>Dashboard</span>
            </Link>
          )}
          {isAuthenticated && user?.role === 'ADMIN' && (
            <Link to="/admin/dashboard" className="flex flex-col items-center px-1">
              <LayoutDashboard className={`h-6 w-6 ${location.pathname.startsWith('/admin') ? 'text-medical-primary dark:text-medical-accent' : 'text-medical-neutral-500 dark:text-medical-dark-text-secondary'}`} />
              <span className={`text-xs mt-1 ${location.pathname.startsWith('/admin') ? 'text-medical-primary dark:text-medical-accent font-medium' : 'text-medical-neutral-500 dark:text-medical-dark-text-secondary'}`}>Admin</span>
            </Link>
          )}
          <Link to="/about" className="flex flex-col items-center px-1">
            <Info className={`h-6 w-6 ${location.pathname === '/about' ? 'text-medical-primary dark:text-medical-accent' : 'text-medical-neutral-500 dark:text-medical-dark-text-secondary'}`} />
            <span className={`text-xs mt-1 ${location.pathname === '/about' ? 'text-medical-primary dark:text-medical-accent font-medium' : 'text-medical-neutral-500 dark:text-medical-dark-text-secondary'}`}>About</span>
          </Link>
          <Link to="/contact" className="flex flex-col items-center px-1">
            <Phone className={`h-6 w-6 ${location.pathname === '/contact' ? 'text-medical-primary dark:text-medical-accent' : 'text-medical-neutral-500 dark:text-medical-dark-text-secondary'}`} />
            <span className={`text-xs mt-1 ${location.pathname === '/contact' ? 'text-medical-primary dark:text-medical-accent font-medium' : 'text-medical-neutral-500 dark:text-medical-dark-text-secondary'}`}>Contact</span>
          </Link>
        </div>
        <div className="h-16"></div>
      </>
    );
  }

  // Desktop navigation - improved formatting
  return (
    <nav className="bg-white dark:bg-medical-dark-surface py-3 shadow-md sticky top-0 z-50 border-b border-medical-border-light dark:border-medical-dark-border">
      <div className="container mx-auto px-6 flex justify-between items-center">
        {/* Logo */}
        <Link to="/" className="flex items-center gap-3 hover:opacity-80 transition-opacity">
          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-medical-primary to-medical-secondary flex items-center justify-center text-white font-bold text-lg shadow-lg">F</div>
          <span className="font-bold text-2xl text-medical-primary dark:text-medical-accent">Dr. Fintan</span>
        </Link>
        
        {/* Navigation Links */}
        <div className="hidden lg:flex items-center space-x-8">
          <Link 
            to="/" 
            className={`text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium transition-colors px-3 py-2 rounded-md hover:bg-medical-bg-light dark:hover:bg-medical-dark-surface/50 ${
              location.pathname === '/' ? 'text-medical-primary dark:text-medical-accent bg-medical-bg-light dark:bg-medical-dark-surface/30' : ''
            }`}
          >
            Home
          </Link>
          <Link 
            to="/about" 
            className={`text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium transition-colors px-3 py-2 rounded-md hover:bg-medical-bg-light dark:hover:bg-medical-dark-surface/50 ${
              location.pathname === '/about' ? 'text-medical-primary dark:text-medical-accent bg-medical-bg-light dark:bg-medical-dark-surface/30' : ''
            }`}
          >
            About
          </Link>
          <Link 
            to="/contact" 
            className={`text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium transition-colors px-3 py-2 rounded-md hover:bg-medical-bg-light dark:hover:bg-medical-dark-surface/50 ${
              location.pathname === '/contact' ? 'text-medical-primary dark:text-medical-accent bg-medical-bg-light dark:bg-medical-dark-surface/30' : ''
            }`}
          >
            Contact
          </Link>
          {isAuthenticated && user?.role === 'PATIENT' && (
            <Link 
              to="/patient-dashboard" 
              className={`text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium transition-colors px-3 py-2 rounded-md hover:bg-medical-bg-light dark:hover:bg-medical-dark-surface/50 ${
                location.pathname === '/patient-dashboard' ? 'text-medical-primary dark:text-medical-accent bg-medical-bg-light dark:bg-medical-dark-surface/30' : ''
              }`}
            >
              Dashboard
            </Link>
          )}
          {isAuthenticated && user?.role === 'ADMIN' && (
            <Link 
              to="/admin/dashboard" 
              className={`text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium transition-colors px-3 py-2 rounded-md hover:bg-medical-bg-light dark:hover:bg-medical-dark-surface/50 ${
                location.pathname.startsWith('/admin') ? 'text-medical-primary dark:text-medical-accent bg-medical-bg-light dark:bg-medical-dark-surface/30' : ''
              }`}
            >
              Admin Dashboard
            </Link>
          )}
        </div>
        
        {/* Auth & Action Buttons */}
        <div className="hidden lg:flex items-center space-x-4">
          <ThemeToggle />
          
          {/* Auth Buttons */}
          <div className="flex items-center space-x-3 border-r border-medical-border-light dark:border-medical-dark-border pr-4">
            {isAuthenticated ? (
              <Button 
                variant="ghost" 
                className="text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent hover:bg-medical-bg-light dark:hover:bg-medical-dark-surface/50 font-medium"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            ) : (
              <>
                <Link to="/auth/login">
                  <Button 
                    variant="ghost" 
                    className="text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent hover:bg-medical-bg-light dark:hover:bg-medical-dark-surface/50 font-medium"
                  >
                    <LogIn className="h-4 w-4 mr-2" />
                    Login
                  </Button>
                </Link>
                <Link to="/auth/register">
                  <Button 
                    variant="outline" 
                    className="border-medical-primary text-medical-primary hover:bg-medical-primary hover:text-white dark:border-medical-accent dark:text-medical-accent dark:hover:bg-medical-accent dark:hover:text-white font-medium transition-all duration-200"
                  >
                    <UserPlus className="h-4 w-4 mr-2" />
                    Sign Up
                  </Button>
                </Link>
              </>
            )}
          </div>
          
          {/* CTA Button */}
          <Link to="/booking">
            <Button className="bg-medical-primary hover:bg-medical-primary/90 text-white dark:bg-medical-accent dark:hover:bg-medical-accent/90 font-semibold px-6 py-2.5 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
              <Calendar className="h-4 w-4 mr-2" />
              Book Consultation
            </Button>
          </Link>
        </div>
        
        {/* Mobile Menu Toggle */}
        <div className="lg:hidden flex items-center gap-3">
          <ThemeToggle />
          <button 
            onClick={toggleMenu}
            className="p-2 rounded-md hover:bg-medical-bg-light dark:hover:bg-medical-dark-surface/50 transition-colors"
          >
            {isMenuOpen ? 
              <X className="h-6 w-6 text-medical-neutral-600 dark:text-medical-dark-text-primary" /> : 
              <Menu className="h-6 w-6 text-medical-neutral-600 dark:text-medical-dark-text-primary" />
            }
          </button>
        </div>
      </div>
      
      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white dark:bg-medical-dark-surface w-full py-6 px-6 shadow-lg animate-fade-in border-t border-medical-border-light dark:border-medical-dark-border">
          <div className="flex flex-col space-y-4">
            <Link 
              to="/" 
              className="text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium py-3 px-4 rounded-lg hover:bg-medical-bg-light dark:hover:bg-medical-dark-surface/50 transition-all" 
              onClick={toggleMenu}
            >
              Home
            </Link>
            <Link 
              to="/about" 
              className="text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium py-3 px-4 rounded-lg hover:bg-medical-bg-light dark:hover:bg-medical-dark-surface/50 transition-all" 
              onClick={toggleMenu}
            >
              About
            </Link>
            <Link 
              to="/contact" 
              className="text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium py-3 px-4 rounded-lg hover:bg-medical-bg-light dark:hover:bg-medical-dark-surface/50 transition-all" 
              onClick={toggleMenu}
            >
              Contact
            </Link>
            {isAuthenticated && user?.role === 'PATIENT' && (
              <Link to="/patient-dashboard" className="text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium py-3 px-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800" onClick={toggleMenu}>
                Dashboard
              </Link>
            )}
            {isAuthenticated && user?.role === 'ADMIN' && (
              <Link to="/admin/dashboard" className="text-medical-neutral-600 hover:text-medical-primary dark:text-medical-dark-text-primary dark:hover:text-medical-accent font-medium py-3 px-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800" onClick={toggleMenu}>
                Admin Dashboard
              </Link>
            )}
            
            <div className="border-t dark:border-gray-700 my-4"></div>
            
            <div className="flex flex-col space-y-3">
              {isAuthenticated ? (
                <Button 
                  variant="outline" 
                  className="w-full border-red-500 text-red-500 hover:bg-red-500 hover:text-white py-3"
                  onClick={handleLogout}
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </Button>
              ) : (
                <>
                  <Link to="/auth/login">
                    <Button 
                      variant="outline" 
                      className="w-full border-medical-primary text-medical-primary hover:bg-medical-primary hover:text-white dark:border-medical-accent dark:text-medical-accent dark:hover:bg-medical-accent dark:hover:text-white py-3"
                      onClick={toggleMenu}
                    >
                      <LogIn className="h-4 w-4 mr-2" />
                      Login
                    </Button>
                  </Link>
                  <Link to="/auth/register">
                    <Button 
                      variant="outline" 
                      className="w-full border-medical-secondary text-medical-secondary hover:bg-medical-secondary hover:text-white py-3"
                      onClick={toggleMenu}
                    >
                      <UserPlus className="h-4 w-4 mr-2" />
                      Sign Up
                    </Button>
                  </Link>
                </>
              )}
              <Link to="/booking">
                <Button 
                  className="bg-medical-primary hover:bg-medical-primary/90 text-white w-full dark:bg-medical-accent dark:hover:bg-medical-accent/90 py-3 font-semibold" 
                  onClick={toggleMenu}
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Book Consultation
                </Button>
              </Link>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
