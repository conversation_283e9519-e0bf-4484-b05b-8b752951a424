import React from 'react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Loader2, AlertCircle, RefreshCw, Wifi, WifiOff } from 'lucide-react';

// Generic loading spinner component
export const LoadingSpinner: React.FC<{ 
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}> = ({ size = 'md', text, className = '' }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  return (
    <div className={`flex items-center justify-center gap-2 ${className}`}>
      <Loader2 className={`animate-spin ${sizeClasses[size]}`} />
      {text && <span className="text-sm text-muted-foreground">{text}</span>}
    </div>
  );
};

// Full page loading state
export const PageLoading: React.FC<{ message?: string }> = ({ 
  message = 'Loading...' 
}) => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="flex flex-col items-center gap-4">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
      <p className="text-lg font-medium">{message}</p>
    </div>
  </div>
);

// Card loading skeleton
export const CardSkeleton: React.FC<{ 
  showHeader?: boolean;
  lines?: number;
  className?: string;
}> = ({ showHeader = true, lines = 3, className = '' }) => (
  <Card className={className}>
    {showHeader && (
      <CardHeader>
        <Skeleton className="h-6 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </CardHeader>
    )}
    <CardContent className="space-y-3">
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton key={i} className="h-4 w-full" />
      ))}
    </CardContent>
  </Card>
);

// Table loading skeleton
export const TableSkeleton: React.FC<{ 
  rows?: number;
  columns?: number;
}> = ({ rows = 5, columns = 4 }) => (
  <div className="space-y-3">
    {/* Header */}
    <div className="flex gap-4">
      {Array.from({ length: columns }).map((_, i) => (
        <Skeleton key={i} className="h-6 flex-1" />
      ))}
    </div>
    {/* Rows */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div key={rowIndex} className="flex gap-4">
        {Array.from({ length: columns }).map((_, colIndex) => (
          <Skeleton key={colIndex} className="h-8 flex-1" />
        ))}
      </div>
    ))}
  </div>
);

// Error state component
export const ErrorState: React.FC<{
  title?: string;
  message: string;
  onRetry?: () => void;
  retryText?: string;
  showIcon?: boolean;
  className?: string;
}> = ({ 
  title = 'Something went wrong',
  message,
  onRetry,
  retryText = 'Try again',
  showIcon = true,
  className = ''
}) => (
  <Card className={className}>
    <CardContent className="p-8 text-center">
      {showIcon && (
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
      )}
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-gray-600 dark:text-gray-400 mb-4">{message}</p>
      {onRetry && (
        <Button onClick={onRetry} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          {retryText}
        </Button>
      )}
    </CardContent>
  </Card>
);

// Empty state component
export const EmptyState: React.FC<{
  icon?: React.ReactNode;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}> = ({ icon, title, description, action, className = '' }) => (
  <Card className={className}>
    <CardContent className="p-8 text-center">
      {icon && <div className="mb-4">{icon}</div>}
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      {description && (
        <p className="text-gray-600 dark:text-gray-400 mb-4">{description}</p>
      )}
      {action && (
        <Button onClick={action.onClick}>
          {action.label}
        </Button>
      )}
    </CardContent>
  </Card>
);

// Network status indicator
export const NetworkStatus: React.FC<{ 
  isOnline: boolean;
  className?: string;
}> = ({ isOnline, className = '' }) => (
  <div className={`flex items-center gap-2 text-sm ${className}`}>
    {isOnline ? (
      <>
        <Wifi className="h-4 w-4 text-green-500" />
        <span className="text-green-600">Online</span>
      </>
    ) : (
      <>
        <WifiOff className="h-4 w-4 text-red-500" />
        <span className="text-red-600">Offline</span>
      </>
    )}
  </div>
);

// Data refresh indicator
export const RefreshIndicator: React.FC<{
  isRefreshing: boolean;
  onRefresh: () => void;
  lastUpdated?: Date;
  className?: string;
}> = ({ isRefreshing, onRefresh, lastUpdated, className = '' }) => (
  <div className={`flex items-center gap-2 text-sm text-muted-foreground ${className}`}>
    <Button
      variant="ghost"
      size="sm"
      onClick={onRefresh}
      disabled={isRefreshing}
      className="h-8 w-8 p-0"
    >
      <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
    </Button>
    {lastUpdated && (
      <span>
        Updated {lastUpdated.toLocaleTimeString()}
      </span>
    )}
  </div>
);

// Loading overlay for existing content
export const LoadingOverlay: React.FC<{
  isLoading: boolean;
  children: React.ReactNode;
  message?: string;
}> = ({ isLoading, children, message = 'Loading...' }) => (
  <div className="relative">
    {children}
    {isLoading && (
      <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-10">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="text-sm font-medium">{message}</span>
        </div>
      </div>
    )}
  </div>
);

// Inline loading state for buttons
export const ButtonLoading: React.FC<{
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
}> = ({ isLoading, children, loadingText }) => (
  <>
    {isLoading ? (
      <>
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        {loadingText || children}
      </>
    ) : (
      children
    )}
  </>
);
