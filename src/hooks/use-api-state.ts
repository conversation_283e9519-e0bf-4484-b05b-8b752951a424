import { useState, useCallback, useRef, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { errorTransforms } from '@/lib/utils/dataTransforms';

// Generic API state interface
export interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

// Options for API calls
export interface ApiOptions {
  showToastOnError?: boolean;
  showToastOnSuccess?: boolean;
  successMessage?: string;
  retryCount?: number;
  retryDelay?: number;
}

// Hook for managing API call states
export function useApiState<T>(
  initialData: T | null = null,
  defaultOptions: ApiOptions = {}
) {
  const { toast } = useToast();
  const [state, setState] = useState<ApiState<T>>({
    data: initialData,
    loading: false,
    error: null,
    lastUpdated: null,
  });
  
  const abortControllerRef = useRef<AbortController | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);

  // Execute API call with state management
  const execute = useCallback(async <R = T>(
    apiCall: (signal?: AbortSignal) => Promise<R>,
    options: ApiOptions = {}
  ): Promise<R | null> => {
    const opts = { ...defaultOptions, ...options };
    
    // Cleanup any existing requests
    cleanup();
    
    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
    }));

    const attemptCall = async (attempt: number = 1): Promise<R | null> => {
      try {
        const result = await apiCall(abortControllerRef.current?.signal);
        
        setState(prev => ({
          ...prev,
          data: result as T,
          loading: false,
          error: null,
          lastUpdated: new Date(),
        }));

        if (opts.showToastOnSuccess && opts.successMessage) {
          toast({
            title: 'Success',
            description: opts.successMessage,
          });
        }

        return result;
      } catch (error: any) {
        // Don't handle aborted requests
        if (error.name === 'AbortError') {
          return null;
        }

        const errorMessage = errorTransforms.apiError(error);
        
        // Retry logic
        const maxRetries = opts.retryCount || 0;
        if (attempt <= maxRetries) {
          const delay = opts.retryDelay || 1000 * attempt;
          
          retryTimeoutRef.current = setTimeout(() => {
            attemptCall(attempt + 1);
          }, delay);
          
          return null;
        }

        setState(prev => ({
          ...prev,
          loading: false,
          error: errorMessage,
        }));

        if (opts.showToastOnError !== false) {
          toast({
            title: 'Error',
            description: errorMessage,
            variant: 'destructive',
          });
        }

        return null;
      }
    };

    return attemptCall();
  }, [defaultOptions, cleanup, toast]);

  // Reset state
  const reset = useCallback(() => {
    cleanup();
    setState({
      data: initialData,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, [initialData, cleanup]);

  // Set data manually
  const setData = useCallback((data: T | null) => {
    setState(prev => ({
      ...prev,
      data,
      lastUpdated: new Date(),
    }));
  }, []);

  // Set error manually
  const setError = useCallback((error: string | null) => {
    setState(prev => ({
      ...prev,
      error,
      loading: false,
    }));
  }, []);

  // Set loading manually
  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({
      ...prev,
      loading,
    }));
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    ...state,
    execute,
    reset,
    setData,
    setError,
    setLoading,
    isSuccess: !state.loading && !state.error && state.data !== null,
    isEmpty: !state.loading && !state.error && state.data === null,
  };
}

// Hook for managing multiple API states (useful for dashboards)
export function useMultiApiState<T extends Record<string, any>>(
  initialStates: { [K in keyof T]: T[K] | null }
) {
  const [states, setStates] = useState<{ [K in keyof T]: ApiState<T[K]> }>(() => {
    const initial = {} as { [K in keyof T]: ApiState<T[K]> };
    for (const key in initialStates) {
      initial[key] = {
        data: initialStates[key],
        loading: false,
        error: null,
        lastUpdated: null,
      };
    }
    return initial;
  });

  const { toast } = useToast();

  // Execute API call for a specific key
  const execute = useCallback(async <K extends keyof T>(
    key: K,
    apiCall: () => Promise<T[K]>,
    options: ApiOptions = {}
  ): Promise<T[K] | null> => {
    setStates(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        loading: true,
        error: null,
      },
    }));

    try {
      const result = await apiCall();
      
      setStates(prev => ({
        ...prev,
        [key]: {
          ...prev[key],
          data: result,
          loading: false,
          error: null,
          lastUpdated: new Date(),
        },
      }));

      if (options.showToastOnSuccess && options.successMessage) {
        toast({
          title: 'Success',
          description: options.successMessage,
        });
      }

      return result;
    } catch (error: any) {
      const errorMessage = errorTransforms.apiError(error);
      
      setStates(prev => ({
        ...prev,
        [key]: {
          ...prev[key],
          loading: false,
          error: errorMessage,
        },
      }));

      if (options.showToastOnError !== false) {
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      }

      return null;
    }
  }, [toast]);

  // Reset specific state
  const reset = useCallback(<K extends keyof T>(key: K) => {
    setStates(prev => ({
      ...prev,
      [key]: {
        data: initialStates[key],
        loading: false,
        error: null,
        lastUpdated: null,
      },
    }));
  }, [initialStates]);

  // Reset all states
  const resetAll = useCallback(() => {
    const resetStates = {} as { [K in keyof T]: ApiState<T[K]> };
    for (const key in initialStates) {
      resetStates[key] = {
        data: initialStates[key],
        loading: false,
        error: null,
        lastUpdated: null,
      };
    }
    setStates(resetStates);
  }, [initialStates]);

  // Get loading state for all or specific keys
  const isLoading = useCallback((keys?: (keyof T)[]) => {
    const keysToCheck = keys || Object.keys(states) as (keyof T)[];
    return keysToCheck.some(key => states[key].loading);
  }, [states]);

  // Get error state for all or specific keys
  const hasError = useCallback((keys?: (keyof T)[]) => {
    const keysToCheck = keys || Object.keys(states) as (keyof T)[];
    return keysToCheck.some(key => states[key].error !== null);
  }, [states]);

  return {
    states,
    execute,
    reset,
    resetAll,
    isLoading,
    hasError,
  };
}

export default useApiState;
