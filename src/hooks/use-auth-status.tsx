import { useQuery, useQueryClient } from '@tanstack/react-query';
import authService from '../services/authService'; // Adjust path as needed
import axios from 'axios';

interface User {
  id: string;
  email: string;
  role: string;
  name?: string;
}

const fetchCurrentUser = async (): Promise<User | null> => {
  const token = authService.getToken();
  if (!token) {
    return null;
  }

  try {
    const response = await axios.get(import.meta.env.VITE_API_BASE_URL + '/users/me', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data.user;
  } catch (error) {
    // If token is invalid or expired, clear it
    authService.logout();
    return null;
  }
};

export const useAuthStatus = () => {
  const queryClient = useQueryClient();

  const { data: user, isLoading, isError, refetch } = useQuery<User | null, Error>({
    queryKey: ['currentUser'],
    queryFn: fetchCurrentUser,
    staleTime: 1000 * 60 * 5, // Data considered fresh for 5 minutes
    gcTime: 1000 * 60 * 10, // Cache data for 10 minutes (garbage collection time)
    retry: 1, // Retry once on failure
  });

  const isAuthenticated = !!user;

  const invalidateAuth = () => {
    queryClient.invalidateQueries({ queryKey: ['currentUser'] });
  };

  return { user, isAuthenticated, isLoading, isError, refetch, invalidateAuth };
};
