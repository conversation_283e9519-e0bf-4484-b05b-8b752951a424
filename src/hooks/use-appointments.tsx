
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  appointmentApiService,
  type CreateAppointmentRequest,
  type UpdateAppointmentRequest,
  type Appointment
} from '@/services';

export const useAppointments = () => {
  const queryClient = useQueryClient();

  const appointments = useQuery({
    queryKey: ['appointments'],
    queryFn: async () => {
      const response = await appointmentApiService.getMyAppointments();
      return response.data?.appointments || [];
    },
  });

  const upcomingAppointments = useQuery({
    queryKey: ['appointments', 'upcoming'],
    queryFn: async () => {
      const response = await appointmentApiService.getUpcomingAppointments();
      return response.data || [];
    },
  });

  const appointmentById = (id: string) => useQuery({
    queryKey: ['appointments', id],
    queryFn: async () => {
      const response = await appointmentApiService.getAppointmentById(id);
      return response.data;
    },
    enabled: !!id,
  });

  const createAppointment = useMutation({
    mutationFn: async (newAppointment: CreateAppointmentRequest) => {
      const response = await appointmentApiService.createAppointment(newAppointment);
      if (!response.success) {
        throw new Error(response.message || 'Failed to create appointment');
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
    },
  });

  const updateAppointment = useMutation({
    mutationFn: async ({ id, data }: { id: string, data: UpdateAppointmentRequest }) => {
      const response = await appointmentApiService.updateAppointment(id, data);
      if (!response.success) {
        throw new Error(response.message || 'Failed to update appointment');
      }
      return response.data;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['appointments', id] });
    },
  });

  const cancelAppointment = useMutation({
    mutationFn: async ({ id, reason }: { id: string, reason?: string }) => {
      const response = await appointmentApiService.cancelAppointment(id, reason);
      if (!response.success) {
        throw new Error(response.message || 'Failed to cancel appointment');
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
    },
  });

  return {
    appointments,
    upcomingAppointments,
    appointmentById,
    createAppointment,
    updateAppointment,
    cancelAppointment
  };
};
