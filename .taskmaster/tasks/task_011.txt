# Task ID: 11
# Title: Patient Dashboard Integration
# Status: pending
# Dependencies: 9, 10
# Priority: medium
# Description: Integrate the Patient Dashboard frontend components with the backend APIs to display real appointment data, medical history, enable consultation scheduling, and show payment history.
# Details:
Develop React components for the Patient Dashboard. Use `useEffect` and `axios` to fetch data from the backend APIs (e.g., `/api/appointments?patientId=...`, `/api/medical-records?patientId=...`, `/api/payments?patientId=...`). Display fetched data in tables or lists. Implement forms and API calls for scheduling new appointments (`/api/appointments.post.ts`). Ensure data is filtered to show only the logged-in patient's information. Implement loading states and error handling for API calls.

# Test Strategy:
Log in as a Patient. Verify that the dashboard displays accurate appointment history, medical records, and payment history fetched from the database. Test scheduling a new appointment and confirm it appears in the list and is persisted in the database. Verify that a patient cannot view data belonging to other patients.
