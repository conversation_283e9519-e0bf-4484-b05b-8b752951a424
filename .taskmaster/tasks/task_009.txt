# Task ID: 9
# Title: Frontend Authentication State Management
# Status: pending
# Dependencies: 8
# Priority: high
# Description: Implement React Context API for managing the global authentication state, including token storage, retrieval, and logic for protected routes and role-based UI rendering.
# Details:
Create an `AuthContext.tsx` using `React.createContext`. This context should provide: `isAuthenticated` (boolean), `user` (object with `id`, `email`, `role`), `login` function, `logout` function. The `login` function should make the API call and, on success, update the context state. Since JWT is in `HttpOnly` cookies, the frontend won't directly access it. Instead, the `AuthContext` can check authentication status by making a lightweight API call to a protected endpoint (e.g., `/api/auth/me`) on app load or by relying on the backend's cookie-based session. Implement a `ProtectedRoute` component using `react-router-dom@6.x.x` that checks `isAuthenticated` and redirects unauthenticated users. Use the `user.role` from context to conditionally render UI elements.

# Test Strategy:
Verify that after login, the user's authentication state is correctly reflected across the application. Test protected routes by attempting to navigate to them directly when logged out (should redirect). Test role-based UI elements by logging in as different roles (Patient, Provider, Admin) and verifying correct components are shown/hidden. Test session persistence by refreshing the browser after login.
