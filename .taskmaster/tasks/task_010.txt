# Task ID: 10
# Title: Core Data Models and CRUD APIs
# Status: pending
# Dependencies: 2, 7
# Priority: medium
# Description: Define comprehensive Prisma data models for Patient, Provider, Appointment, MedicalRecord, Prescription, Payment, and Notification systems, and implement their respective RESTful CRUD API endpoints.
# Details:
Update `schema.prisma` to include models for `Patient` (one-to-one with `User`), `Provider` (one-to-one with `User`), `Appointment`, `MedicalRecord`, `Prescription`, `Payment`, `Notification`. Define relationships between these models (e.g., `Appointment` linked to `Patient` and `Provider`). Run `npx prisma migrate dev` to apply schema changes. For each model, create corresponding RESTful API endpoints in `server/api` (e.g., `server/api/patients/[id].get.ts`, `server/api/appointments.post.ts`). Implement CRUD operations using Prisma Client. Ensure all these new API endpoints are protected by the authentication middleware and enforce appropriate role-based access (e.g., only Providers can create Medical Records, Patients can only view their own).

# Test Strategy:
For each new model, use Postman/Insomnia to test all CRUD operations (Create, Read, Update, Delete) via their respective API endpoints. Verify data persistence in the Neon database. Test access control by attempting operations with different user roles (e.g., Patient trying to create a Provider record) and ensure appropriate 401/403 errors are returned.
