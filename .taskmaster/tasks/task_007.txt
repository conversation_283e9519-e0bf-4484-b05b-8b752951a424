# Task ID: 7
# Title: Authentication Middleware and Protected Routes (Backend)
# Status: done
# Dependencies: 4, 6
# Priority: high
# Description: Implement backend middleware to protect API routes by validating JWTs from incoming requests and enforcing role-based access control.
# Details:
Create `server/middleware/auth.ts`. This middleware should: 1. Extract the JWT from the `Authorization` header (Bearer token) or from an `HttpOnly` cookie (`getCookie(event, 'token')`). 2. Verify the token's authenticity and expiration using `verifyToken` utility. 3. If valid, attach user information (e.g., `userId`, `role`) to the `event.context` for subsequent handlers to use. 4. If invalid or missing, throw an `H3Error` with a 401 Unauthorized status. Implement role-based access control (RBAC) by checking `event.context.user.role` against required roles for specific routes or within individual API handlers. Example: `if (event.context.user.role !== 'ADMIN') { throw createError({ statusCode: 403, statusMessage: 'Forbidden' }); }`.

# Test Strategy:
Create a simple protected API endpoint. Test accessing it: 1. Without any token (should be denied). 2. With an invalid/expired token (should be denied). 3. With a valid token (should be granted). 4. With a valid token but incorrect role for RBAC (should be denied with 403 Forbidden). Verify `event.context.user` contains correct user data in protected handlers.
