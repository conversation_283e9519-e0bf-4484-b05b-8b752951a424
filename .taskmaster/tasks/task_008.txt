# Task ID: 8
# Title: Frontend Authentication Forms and API Integration
# Status: done
# Dependencies: 5, 6
# Priority: high
# Description: Develop the frontend React components for user registration and login forms, integrating them with the backend authentication API endpoints.
# Details:
Create React components for `RegistrationForm.tsx` and `LoginForm.tsx`. Use `useState` for form input management and `axios` (`npm install axios@1.6.5`) or `fetch` for API calls. Implement client-side validation for form fields (e.g., email format, password strength). Handle form submission by sending POST requests to `/api/auth/register` and `/api/auth/login`. Display user-friendly error messages based on API responses. After successful login, redirect the user to their respective dashboard.

# Test Strategy:
Manually test registration and login flows in the browser. Verify form validation works correctly. Confirm successful registration creates a user in the database. Confirm successful login redirects to the dashboard and sets the `HttpOnly` cookie. Test error scenarios (e.g., invalid credentials, network errors) and ensure appropriate feedback is displayed to the user.
