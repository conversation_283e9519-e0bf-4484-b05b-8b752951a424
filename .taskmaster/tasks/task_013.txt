# Task ID: 13
# Title: Admin Dashboard and User Management
# Status: pending
# Dependencies: 9, 10
# Priority: medium
# Description: Implement the Admin Dashboard functionality for user management, system analytics, platform configuration, and audit logs, connecting to the backend APIs.
# Details:
Develop React components for the Admin Dashboard. Implement user management features (list all users, create/update/delete users, change user roles) by interacting with dedicated backend API endpoints (e.g., `/api/admin/users`). Implement placeholder components for system analytics, platform configuration, and audit logs, which will fetch data from future or existing backend endpoints. Ensure all admin functionalities are strictly protected by the authentication middleware, allowing access only to users with the 'ADMIN' role.

# Test Strategy:
Log in as an Admin. Verify the ability to view, create, update, and delete users of all roles (Patient, Provider, Admin). Test changing a user's role. Confirm all changes are persisted in the database. Attempt to access admin functionalities as a non-admin user (Patient/Provider) and verify access is denied with a 403 Forbidden error.
