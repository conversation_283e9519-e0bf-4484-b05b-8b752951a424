# Task ID: 6
# Title: Backend User Login API
# Status: done
# Dependencies: 2, 3, 4
# Priority: high
# Description: Develop the backend API endpoint for user login, handling password verification, JWT generation, and setting secure HTTP-only cookies for session management.
# Details:
Create `server/api/auth/login.post.ts`. This endpoint should: 1. Read request body (email, password). 2. Find the user by email using Prisma. 3. Compare the provided password with the stored hashed password using `comparePassword` utility. 4. If credentials are valid, generate a JWT using `generateToken` (e.g., payload: `{ userId: user.id, role: user.role }`, expiresIn: '1h'). 5. Set the JWT as an `HttpOnly` cookie using `setCookie(event, 'token', jwtToken, { httpOnly: true, secure: process.env.NODE_ENV === 'production', sameSite: 'Lax', maxAge: 3600 })`. 6. Return a success response (e.g., user ID, role) or an authentication failure message. Consider implementing a refresh token mechanism for longer sessions, stored in a separate `HttpOnly` cookie or database.

# Test Strategy:
Use Postman/Insomnia to test login with correct and incorrect credentials. Verify that successful login returns a success message and sets an `HttpOnly` cookie containing the JWT. Attempt to access the cookie from client-side JavaScript to confirm `HttpOnly` protection. Test session expiration by setting a short `maxAge` and verifying logout.
