# Task ID: 2
# Title: Neon PostgreSQL and Prisma ORM Setup
# Status: done
# Dependencies: 1
# Priority: high
# Description: Configure Prisma ORM to connect with Neon PostgreSQL, define the initial database schema for core entities, and set up migration management.
# Details:
Install Prisma CLI (`npm install -g prisma`) and Prisma client (`npm install @prisma/client`). Configure `schema.prisma` to connect to Neon PostgreSQL using the `DATABASE_URL` environment variable. Define the initial `User` model with fields like `id`, `email`, `passwordHash`, `role` (enum: `PATIENT`, `PROVIDER`, `ADMIN`), `createdAt`, `updatedAt`. Run `npx prisma migrate dev --name init` to create the initial migration and apply it to the Neon database. Ensure connection pooling is implicitly handled by Prisma's connection management or explicitly configured if needed for high concurrency (Prisma Client generally handles pooling efficiently).

# Test Strategy:
Successfully run `npx prisma migrate dev` and `npx prisma db push` (for development) or `npx prisma migrate deploy` (for production). Connect to the Neon database using a client (e.g., DBeaver, pgAdmin) and verify the `User` table and its columns exist as defined in `schema.prisma`.
