# Task ID: 5
# Title: Backend User Registration API
# Status: done
# Dependencies: 2, 3, 4
# Priority: high
# Description: Develop the backend API endpoint for user registration, including input validation, password hashing, role assignment, and persistence of user data to the database via Prisma.
# Details:
Create `server/api/auth/register.post.ts`. This endpoint should: 1. Read request body (`readBody(event)`). 2. Validate input (email, password, role). Use a schema validation library like `zod` (`npm install zod@3.22.4`) for robust validation. 3. Hash the password using `hashPassword` utility. 4. Check if a user with the given email already exists using Prisma. 5. Create a new user record in the database using `prisma.user.create` with the hashed password and assigned role. 6. Return a success response (e.g., user ID, email) or appropriate error messages. Ensure roles are validated against the `Role` enum defined in Prisma.

# Test Strategy:
Use Postman/Insomnia or `curl` to send valid and invalid registration requests. Verify that valid requests create a user in the database with a hashed password and correct role. Test edge cases: existing email, weak password, invalid role, missing fields. Confirm appropriate error messages are returned for invalid inputs.
