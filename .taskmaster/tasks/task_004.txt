# Task ID: 4
# Title: Password Hashing and JWT Utilities
# Status: done
# Dependencies: 1
# Priority: high
# Description: Implement secure password hashing using bcrypt and develop utilities for JWT token generation, signing, and verification for authentication purposes.
# Details:
Install `bcryptjs` (`npm install bcryptjs@2.4.3`) for password hashing and `jsonwebtoken` (`npm install jsonwebtoken@9.0.2`) for JWT. Create a `server/utils/auth.ts` file. Implement functions: `hashPassword(password: string): Promise<string>` using `bcryptjs.hash(password, 10)` (salt rounds 10-12 recommended), `comparePassword(password: string, hash: string): Promise<boolean>` using `bcryptjs.compare`. Implement `generateToken(payload: object, secret: string, expiresIn: string): string` using `jwt.sign` and `verifyToken(token: string, secret: string): object` using `jwt.verify`. Store JWT secret securely in environment variables (`JWT_SECRET`).

# Test Strategy:
Write unit tests for `hashPassword` and `comparePassword` to ensure correct hashing and comparison. Test `generateToken` and `verifyToken` with valid and invalid tokens/secrets to confirm proper token generation and error handling on verification failure.
