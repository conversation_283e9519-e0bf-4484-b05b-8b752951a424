generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id            String         @id @default(cuid())
  email         String?        @unique
  password      String?
  name          String?
  phone         String?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  emailVerified DateTime?
  image         String?
  role          String         @default("PATIENT")
  accounts      Account[]
  notifications Notification[]
  patient       Patient?
  provider      Provider?
  sessions      Session[]
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Patient {
  id           String        @id @default(cuid())
  userId       String        @unique
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  appointments Appointment[]
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Provider {
  id             String        @id @default(cuid())
  userId         String        @unique
  specialization String?
  bio            String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  appointments   Appointment[]
  user           User          @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Appointment {
  id               String        @id @default(cuid())
  patientId        String
  providerId       String
  appointmentDate  DateTime
  reason           String?
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt
  consultationType String        @default("VIDEO")
  status           String        @default("SCHEDULED")
  patient          Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)
  provider         Provider      @relation(fields: [providerId], references: [id], onDelete: Cascade)
  consultation     Consultation?
}

model Consultation {
  id            String      @id @default(cuid())
  appointmentId String      @unique
  roomUrl       String
  notes         String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  videoEnabled  Boolean     @default(false)
  status        String      @default("SCHEDULED")
  appointment   Appointment @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
}

model Notification {
  id        String   @id @default(cuid())
  title     String
  message   String
  type      String
  userId    String
  relatedId String?
  isRead    Boolean  @default(false)
  link      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}
