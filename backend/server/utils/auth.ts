import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

// Environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'fintan_virtual_care_jwt_secret_key_2024_development';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
const REFRESH_TOKEN_SECRET = process.env.REFRESH_TOKEN_SECRET || 'fintan_virtual_care_refresh_token_secret_2024_development';
const REFRESH_TOKEN_EXPIRES_IN = process.env.REFRESH_TOKEN_EXPIRES_IN || '30d';

// Salt rounds for bcrypt (10-12 is recommended for production)
const SALT_ROUNDS = 12;

/**
 * Hash a password using bcrypt
 * @param password - Plain text password to hash
 * @returns Promise<string> - Hashed password
 */
export async function hashPassword(password: string): Promise<string> {
  if (!password || typeof password !== 'string') {
    throw new Error('Password must be a non-empty string');
  }
  
  if (password.length < 6) {
    throw new Error('Password must be at least 6 characters long');
  }
  
  try {
    const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);
    return hashedPassword;
  } catch (error) {
    throw new Error('Failed to hash password');
  }
}

/**
 * Compare a plain text password with a hashed password
 * @param password - Plain text password
 * @param hash - Hashed password to compare against
 * @returns Promise<boolean> - True if passwords match, false otherwise
 */
export async function comparePassword(password: string, hash: string): Promise<boolean> {
  if (!password || !hash) {
    return false;
  }
  
  try {
    const isMatch = await bcrypt.compare(password, hash);
    return isMatch;
  } catch (error) {
    return false;
  }
}

/**
 * Generate a JWT access token
 * @param payload - Data to include in the token
 * @param secret - JWT secret (optional, uses default if not provided)
 * @param expiresIn - Token expiration time (optional, uses default if not provided)
 * @returns string - JWT token
 */
export function generateToken(
  payload: object, 
  secret: string = JWT_SECRET, 
  expiresIn: string = JWT_EXPIRES_IN
): string {
  if (!payload || typeof payload !== 'object') {
    throw new Error('Payload must be a valid object');
  }
  
  if (!secret) {
    throw new Error('JWT secret is required');
  }
  
  try {
    const token = jwt.sign(payload, secret, { 
      expiresIn,
      issuer: 'fintan-virtual-care-hub',
      audience: 'fintan-users'
    });
    return token;
  } catch (error) {
    throw new Error('Failed to generate token');
  }
}

/**
 * Generate a refresh token
 * @param payload - Data to include in the token
 * @returns string - Refresh token
 */
export function generateRefreshToken(payload: object): string {
  return generateToken(payload, REFRESH_TOKEN_SECRET, REFRESH_TOKEN_EXPIRES_IN);
}

/**
 * Verify and decode a JWT token
 * @param token - JWT token to verify
 * @param secret - JWT secret (optional, uses default if not provided)
 * @returns object - Decoded token payload
 */
export function verifyToken(token: string, secret: string = JWT_SECRET): any {
  if (!token || typeof token !== 'string') {
    throw new Error('Token must be a non-empty string');
  }
  
  if (!secret) {
    throw new Error('JWT secret is required');
  }
  
  try {
    const decoded = jwt.verify(token, secret, {
      issuer: 'fintan-virtual-care-hub',
      audience: 'fintan-users'
    });
    return decoded;
  } catch (error: any) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token has expired');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Invalid token');
    } else if (error.name === 'NotBeforeError') {
      throw new Error('Token not active yet');
    } else {
      throw new Error('Token verification failed');
    }
  }
}

/**
 * Verify a refresh token
 * @param token - Refresh token to verify
 * @returns object - Decoded token payload
 */
export function verifyRefreshToken(token: string): any {
  return verifyToken(token, REFRESH_TOKEN_SECRET);
}

/**
 * Extract token from Authorization header
 * @param authHeader - Authorization header value
 * @returns string | null - Extracted token or null if not found
 */
export function extractTokenFromHeader(authHeader: string | undefined): string | null {
  if (!authHeader) {
    return null;
  }
  
  // Expected format: "Bearer <token>"
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }
  
  return parts[1];
}

/**
 * Generate a secure random string for session IDs, etc.
 * @param length - Length of the random string (default: 32)
 * @returns string - Random string
 */
export function generateSecureRandomString(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Create a standardized user payload for JWT tokens
 * @param user - User object from database
 * @returns object - Standardized payload
 */
export function createUserPayload(user: any): object {
  return {
    userId: user.id,
    email: user.email,
    role: user.role,
    name: user.name,
    iat: Math.floor(Date.now() / 1000) // Issued at time
  };
}

// Export constants for use in other modules
export const AUTH_CONSTANTS = {
  JWT_SECRET,
  JWT_EXPIRES_IN,
  REFRESH_TOKEN_SECRET,
  REFRESH_TOKEN_EXPIRES_IN,
  SALT_ROUNDS
} as const;
