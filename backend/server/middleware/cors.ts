import { defineEventHandler, setHeaders, getMethod } from 'h3';

export default defineEventHandler(async (event) => {
  // Get the origin from environment or use default for development
  const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || [
    'http://localhost:5173',
    'http://localhost:3000',
    'http://localhost:8080'
  ];
  
  const origin = event.node.req.headers.origin;
  
  // Set CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Methods': 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400', // 24 hours
  };
  
  // Check if origin is allowed
  if (origin && allowedOrigins.includes(origin)) {
    corsHeaders['Access-Control-Allow-Origin'] = origin;
  } else if (process.env.NODE_ENV === 'development') {
    // In development, allow any origin
    corsHeaders['Access-Control-Allow-Origin'] = origin || '*';
  }
  
  // Set the headers
  setHeaders(event, corsHeaders);
  
  // Handle preflight OPTIONS requests
  if (getMethod(event) === 'OPTIONS') {
    event.node.res.statusCode = 200;
    event.node.res.end();
    return;
  }
});
