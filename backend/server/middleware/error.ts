import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, createError, setResponseStatus } from 'h3';

export default defineEventHandler(async (event) => {
  // This middleware catches unhandled errors
  event.node.res.on('error', (error: any) => {
    console.error('Response error:', error);

    if (!event.node.res.headersSent) {
      setResponseStatus(event, 500);
      return {
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
          details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        }
      };
    }
  });

  // Handle uncaught exceptions in route handlers
  try {
    // Let the request continue to the actual handler
    return;
  } catch (error: any) {
    console.error('Unhandled error in route:', error);

    // Create standardized error response
    const statusCode = error.statusCode || error.status || 500;
    const message = error.message || 'Internal server error';

    throw createError({
      statusCode,
      statusMessage: message,
      data: {
        success: false,
        error: {
          code: error.code || 'INTERNAL_SERVER_ERROR',
          message,
          details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        }
      }
    });
  }
});