import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, H3<PERSON>vent, createError } from 'h3';
import { verifyToken, extractTokenFromHeader } from '../utils/auth';

export default defineEventHandler(async (event: H3Event) => {
  // Define public routes that don't require authentication
  const publicRoutes = [
    '/api/auth/login',
    '/api/auth/register',
    '/api/auth/logout',
    '/api/health'
  ];

  const requestUrl = event.node.req.url || '';

  // Allow access to public routes
  if (publicRoutes.includes(requestUrl)) {
    return;
  }

  try {
    // Extract token from Authorization header
    const authHeader = event.node.req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: 'No token provided',
        data: {
          success: false,
          error: {
            code: 'NO_TOKEN',
            message: 'Authorization token is required'
          }
        }
      });
    }

    // Verify token using auth utility
    const decoded = verifyToken(token);

    // Attach user info to event context for use in route handlers
    event.context.user = decoded;

  } catch (error: any) {
    // Handle token verification errors
    if (error.statusCode) {
      throw error; // Re-throw H3 errors
    }

    // Handle JWT verification errors
    let errorMessage = 'Invalid token';
    let errorCode = 'INVALID_TOKEN';

    if (error.message.includes('expired')) {
      errorMessage = 'Token has expired';
      errorCode = 'TOKEN_EXPIRED';
    } else if (error.message.includes('Invalid token')) {
      errorMessage = 'Invalid token format';
      errorCode = 'INVALID_TOKEN_FORMAT';
    }

    throw createError({
      statusCode: 401,
      statusMessage: errorMessage,
      data: {
        success: false,
        error: {
          code: errorCode,
          message: errorMessage
        }
      }
    });
  }
});
