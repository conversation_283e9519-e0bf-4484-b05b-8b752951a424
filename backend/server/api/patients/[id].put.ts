import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, readBody } from 'h3';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default defineEventHandler(async (event) => {
  const patientId = event.context.params?.id;
  const { name, email, phone, dateOfBirth, address, emergencyContact } = await readBody(event);

  if (!patientId) {
    return { statusCode: 400, body: JSON.stringify({ message: 'Patient ID is required' }) };
  }

  try {
    const patient = await prisma.patient.findUnique({
      where: { id: patientId },
      include: { user: true },
    });

    if (!patient) {
      return { statusCode: 404, body: JSON.stringify({ message: 'Patient not found' }) };
    }

    const updatedPatient = await prisma.$transaction(async (tx) => {
      // Update user information
      if (name || email || phone) {
        await tx.user.update({
          where: { id: patient.userId },
          data: {
            name,
            email,
            phone,
          },
        });
      }

      // Update patient information
      return tx.patient.update({
        where: { id: patientId },
        data: {
          ...(dateOfBirth && { dateOfBirth: new Date(dateOfBirth) }),
          address,
          emergencyContact,
        },
        include: {
          user: true,
        },
      });
    });

    return { statusCode: 200, body: JSON.stringify({ message: 'Patient updated successfully', patient: updatedPatient }) };
  } catch (error) {
    console.error(`Error updating patient with ID ${patientId}:`, error);
    return { statusCode: 500, body: JSON.stringify({ message: 'Internal server error' }) };
  }
});