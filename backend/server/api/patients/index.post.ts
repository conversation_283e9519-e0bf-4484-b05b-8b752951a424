import { defineEvent<PERSON><PERSON><PERSON>, readBody } from 'h3';
import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

export default defineEventHandler(async (event) => {
  const { email, password, name, phone, dateOfBirth, address, emergencyContact } = await readBody(event);

  if (!email || !password || !name) {
    return {
      statusCode: 400,
      body: JSON.stringify({ message: 'Email, password, and name are required' }),
    };
  }

  try {
    const hashedPassword = await bcrypt.hash(password, 10);

    const patient = await prisma.$transaction(async (tx) => {
      const user = await tx.user.create({
        data: {
          email,
          password: hashedPassword,
          name,
          phone,
          role: 'PATIENT', // Use string literal for role
        },
      });

      return tx.patient.create({
        data: {
          userId: user.id,
          ...(dateOfBirth && { dateOfBirth: new Date(dateOfBirth) }), // Conditionally add dateOfBirth
          address,
          emergencyContact,
        },
        include: {
          user: true,
        },
      });
    });

    return { statusCode: 201, body: JSON.stringify({ message: 'Patient registered successfully', patient }) };
  } catch (error) {
    console.error('Error creating patient:', error);
    return { statusCode: 500, body: JSON.stringify({ message: 'Internal server error' }) };
  }
});