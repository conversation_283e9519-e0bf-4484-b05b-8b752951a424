import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default defineEventHandler(async (event) => {
  const patientId = event.context.params?.id;

  if (!patientId) {
    return { statusCode: 400, body: JSON.stringify({ message: 'Patient ID is required' }) };
  }

  try {
    const patient = await prisma.patient.findUnique({
      where: { id: patientId },
      include: {
        user: true,
      },
    });

    if (!patient) {
      return { statusCode: 404, body: JSON.stringify({ message: 'Patient not found' }) };
    }

    return { statusCode: 200, body: JSON.stringify(patient) };
  } catch (error) {
    console.error(`Error fetching patient with ID ${patientId}:`, error);
    return { statusCode: 500, body: JSON.stringify({ message: 'Internal server error' }) };
  }
});