import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default defineEventHandler(async () => {
  try {
    const patients = await prisma.patient.findMany({
      include: {
        user: true,
      },
    });
    return { statusCode: 200, body: JSON.stringify(patients) };
  } catch (error) {
    console.error('Error fetching patients:', error);
    return { statusCode: 500, body: JSON.stringify({ message: 'Internal server error' }) };
  }
});