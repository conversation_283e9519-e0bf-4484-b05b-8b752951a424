import { defineE<PERSON><PERSON><PERSON><PERSON>, readBody, createError, setResponseStatus } from 'h3';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import {
  comparePassword,
  generateToken,
  generateRefreshToken,
  createUserPayload
} from '../../utils/auth';

const prisma = new PrismaClient();

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters long'),
});

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { email, password } = loginSchema.parse(body);

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        patient: true,
        provider: true
      }
    });

    if (!user || !user.password) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid credentials'
      });
    }

    // Verify password using auth utility
    const isPasswordValid = await comparePassword(password, user.password);
    if (!isPasswordValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid credentials'
      });
    }

    // Create user payload for tokens
    const userPayload = createUserPayload(user);

    // Generate tokens using auth utilities
    const accessToken = generateToken(userPayload);
    const refreshToken = generateRefreshToken(userPayload);

    // Prepare user data for response (exclude password)
    const userData = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      phone: user.phone,
      emailVerified: user.emailVerified,
      patient: user.patient,
      provider: user.provider,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    setResponseStatus(event, 200);
    return {
      success: true,
      message: 'Login successful',
      data: {
        user: userData,
        accessToken,
        refreshToken,
        expiresIn: '7d'
      }
    };

  } catch (error: any) {
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Validation error',
        data: {
          success: false,
          errors: error.errors
        }
      });
    }

    // Re-throw H3 errors (like 401 from above)
    if (error.statusCode) {
      throw error;
    }

    console.error('Login error:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    });
  }
});
