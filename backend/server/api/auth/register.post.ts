import { defineE<PERSON><PERSON><PERSON><PERSON>, readBody, createError, setResponseStatus } from 'h3';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import {
  hashPassword,
  generateToken,
  generateRefreshToken,
  createUserPayload
} from '../../utils/auth';

const prisma = new PrismaClient();

const registerSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters long').optional(),
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters long'),
  role: z.enum(['PATIENT', 'PROVIDER', 'ADMIN']).default('PATIENT'),
});

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { name, email, password, role } = registerSchema.parse(body);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw createError({
        statusCode: 409,
        statusMessage: 'User with this email already exists'
      });
    }

    // Hash password using auth utility
    const hashedPassword = await hashPassword(password);

    // Create new user with related records
    const newUser = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role,
        // Create related Patient or Provider record based on role
        patient: role === 'PATIENT' ? { create: {} } : undefined,
        provider: role === 'PROVIDER' ? { create: {} } : undefined,
      },
      include: {
        patient: true,
        provider: true
      }
    });

    // Create user payload for tokens
    const userPayload = createUserPayload(newUser);

    // Generate tokens using auth utilities
    const accessToken = generateToken(userPayload);
    const refreshToken = generateRefreshToken(userPayload);

    // Prepare user data for response (exclude password)
    const userData = {
      id: newUser.id,
      email: newUser.email,
      name: newUser.name,
      role: newUser.role,
      phone: newUser.phone,
      emailVerified: newUser.emailVerified,
      patient: newUser.patient,
      provider: newUser.provider,
      createdAt: newUser.createdAt,
      updatedAt: newUser.updatedAt
    };

    setResponseStatus(event, 201);
    return {
      success: true,
      message: 'Registration successful',
      data: {
        user: userData,
        accessToken,
        refreshToken,
        expiresIn: '7d'
      }
    };

  } catch (error: any) {
    if (error instanceof z.ZodError) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Validation error',
        data: {
          success: false,
          errors: error.errors
        }
      });
    }

    // Re-throw H3 errors (like 409 from above)
    if (error.statusCode) {
      throw error;
    }

    console.error('Registration error:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    });
  }
});
