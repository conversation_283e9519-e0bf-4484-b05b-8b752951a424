import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default defineEventHandler(async () => {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        phone: true,
        createdAt: true,
        updatedAt: true,
      },
    });
    return { statusCode: 200, body: JSON.stringify(users) };
  } catch (error) {
    console.error('Error fetching users:', error);
    return { statusCode: 500, body: JSON.stringify({ message: 'Internal server error' }) };
  }
});