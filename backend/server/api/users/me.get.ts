import { define<PERSON>vent<PERSON><PERSON><PERSON>, createError } from 'h3';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default defineEventHandler(async (event) => {
  try {
    // Get user info from auth middleware context
    const userContext = event.context.user;

    if (!userContext || !userContext.userId) {
      throw createError({
        statusCode: 401,
        statusMessage: 'User not authenticated'
      });
    }

    // Fetch complete user data from database
    const user = await prisma.user.findUnique({
      where: { id: userContext.userId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        phone: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
        patient: {
          select: {
            id: true,
          },
        },
        provider: {
          select: {
            id: true,
            specialization: true,
            bio: true,
          },
        },
      },
    });

    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      });
    }

    return {
      success: true,
      user: user
    };

  } catch (error: any) {
    // Re-throw H3 errors
    if (error.statusCode) {
      throw error;
    }

    console.error('Get current user error:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    });
  }
});
