import { defineEventHand<PERSON>, readBody } from 'h3';
import { PrismaClient, User } from '@prisma/client';
import * as bcrypt from 'bcryptjs';
import { z } from 'zod';

const prisma = new PrismaClient();

const updateUserSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters long').optional(),
  email: z.string().email('Invalid email address').optional(),
  password: z.string().min(6, 'Password must be at least 6 characters long').optional(),
  phone: z.string().optional(),
  role: z.enum(['PATIENT', 'PROVIDER', 'ADMIN']).optional(), // Role can only be changed by ADMIN
});

export default defineEventHandler(async (event) => {
  if (!event.context.user || !event.context.user.userId) {
    return {
      statusCode: 401,
      body: JSON.stringify({ message: 'Unauthorized: User not authenticated' }),
    };
  }

  const targetUserId = event.context.params?.id;
  const authenticatedUserId = event.context.user.userId;
  const authenticatedUserRole = event.context.user.role;
  const body = await readBody(event);

  // A user can only update their own profile, unless they are an ADMIN
  if (authenticatedUserId !== targetUserId && authenticatedUserRole !== 'ADMIN') {
    return {
      statusCode: 403,
      body: JSON.stringify({ message: 'Forbidden: You do not have permission to update this user\'s data' }),
    };
  }

  try {
    const parsedBody = updateUserSchema.parse(body);
    const dataToUpdate: Partial<User> = {};

    if (parsedBody.name !== undefined) dataToUpdate.name = parsedBody.name;
    if (parsedBody.email !== undefined) dataToUpdate.email = parsedBody.email;
    if (parsedBody.phone !== undefined) dataToUpdate.phone = parsedBody.phone;

    if (parsedBody.password) {
      dataToUpdate.password = await bcrypt.hash(parsedBody.password, 10);
    }

    // Only ADMIN can change roles
    if (parsedBody.role !== undefined) {
      if (authenticatedUserRole === 'ADMIN') {
        dataToUpdate.role = parsedBody.role;
      } else {
        return {
          statusCode: 403,
          body: JSON.stringify({ message: 'Forbidden: Only administrators can change user roles' }),
        };
      }
    }

    const updatedUser = await prisma.user.update({
      where: { id: targetUserId },
      data: dataToUpdate,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        phone: true,
        createdAt: true,
        updatedAt: true,
        patient: {
          select: {
            id: true,
          },
        },
        provider: {
          select: {
            id: true,
            specialization: true,
            bio: true,
          },
        },
      },
    });

    return {
      statusCode: 200,
      body: JSON.stringify({ message: 'User updated successfully', user: updatedUser }),
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Validation error', errors: error.errors }),
      };
    }
    console.error('Error updating user:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: 'Internal server error' }),
    };
  }
});
