import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3';
import { PrismaClient, Prisma } from '@prisma/client';

const prisma = new PrismaClient();

export default defineEventHandler(async (event) => {
  if (!event.context.user || !event.context.user.userId) {
    return {
      statusCode: 401,
      body: JSON.stringify({ message: 'Unauthorized: User not authenticated' }),
    };
  }

  const targetUserId = event.context.params?.id;
  const authenticatedUserId = event.context.user.userId;
  const authenticatedUserRole = event.context.user.role;

  // A user can only delete their own profile, unless they are an ADMIN
  if (authenticatedUserId !== targetUserId && authenticatedUserRole !== 'ADMIN') {
    return {
      statusCode: 403,
      body: JSON.stringify({ message: 'Forbidden: You do not have permission to delete this user\'s data' }),
    };
  }

  try {
    await prisma.user.delete({
      where: { id: targetUserId },
    });

    return {
      statusCode: 200,
      body: JSON.stringify({ message: 'User deleted successfully' }),
    };
  } catch (error) {
    console.error('Error deleting user:', error);
    // Check if the error is due to a record not found
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
      return {
        statusCode: 404,
        body: JSON.stringify({ message: 'User not found' }),
      };
    }
    return {
      statusCode: 500,
      body: JSON.stringify({ message: 'Internal server error' }),
    };
  }
});
