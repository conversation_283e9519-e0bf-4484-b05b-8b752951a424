import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default defineEventHandler(async (event) => {
  // The auth middleware should have already attached the user to event.context
  if (!event.context.user || !event.context.user.userId) {
    return {
      statusCode: 401,
      body: JSON.stringify({ message: 'Unauthorized: User not authenticated' }),
    };
  }

  const targetUserId = event.context.params?.id;
  const authenticatedUserId = event.context.user.userId;
  const authenticatedUserRole = event.context.user.role;

  // Allow a user to fetch their own profile
  // Allow ADMIN or PROVIDER to fetch any user's profile
  if (authenticatedUserId !== targetUserId && authenticatedUserRole !== 'ADMIN' && authenticatedUserRole !== 'PROVIDER') {
    return {
      statusCode: 403,
      body: JSON.stringify({ message: 'Forbidden: You do not have permission to access this user\'s data' }),
    };
  }

  try {
    const user = await prisma.user.findUnique({
      where: { id: targetUserId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        phone: true,
        createdAt: true,
        updatedAt: true,
        patient: {
          select: {
            id: true,
          },
        },
        provider: {
          select: {
            id: true,
            specialization: true,
            bio: true,
          },
        },
      },
    });

    if (!user) {
      return {
        statusCode: 404,
        body: JSON.stringify({ message: 'User not found' }),
      };
    }

    return {
      statusCode: 200,
      body: JSON.stringify({ user }),
    };
  } catch (error) {
    console.error('Error fetching user by ID:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: 'Internal server error' }),
    };
  }
});
