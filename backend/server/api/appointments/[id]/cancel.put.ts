import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

const cancelAppointmentSchema = z.object({
  // No specific body parameters needed for cancellation, but we can add if required later
});

export default defineEventHandler(async (event) => {
  const appointmentId = event.context.params?.id;
  const body = await readBody(event); // Read body to trigger validation even if empty

  if (!appointmentId) {
    return { statusCode: 400, body: JSON.stringify({ message: 'Appointment ID is required' }) };
  }

  try {
    cancelAppointmentSchema.parse(body); // Validate empty body

    const cancelledAppointment = await prisma.appointment.update({
      where: { id: appointmentId },
      data: {
        status: 'CANCELLED',
        updatedAt: new Date(),
      },
    });

    // TODO: Send cancellation notifications

    return { statusCode: 200, body: JSON.stringify({ message: 'Appointment cancelled successfully', appointment: cancelledAppointment }) };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Validation error', errors: error.errors }),
      };
    }
    console.error(`Error cancelling appointment with ID ${appointmentId}:`, error);
    return { statusCode: 500, body: JSON.stringify({ message: 'Internal server error' }) };
  }
});