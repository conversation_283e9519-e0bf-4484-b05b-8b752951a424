import { defineEvent<PERSON><PERSON><PERSON>, readBody } from 'h3';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

const updateStatusSchema = z.object({
  status: z.string().min(1, 'Status is required'), // You might want a more specific enum here
});

export default defineEventHandler(async (event) => {
  const appointmentId = event.context.params?.id;
  const body = await readBody(event);

  if (!appointmentId) {
    return { statusCode: 400, body: JSON.stringify({ message: 'Appointment ID is required' }) };
  }

  try {
    const { status } = updateStatusSchema.parse(body);

    const updatedAppointment = await prisma.appointment.update({
      where: { id: appointmentId },
      data: {
        status,
        updatedAt: new Date(),
      },
    });

    return { statusCode: 200, body: JSON.stringify({ message: 'Appointment status updated successfully', appointment: updatedAppointment }) };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Validation error', errors: error.errors }),
      };
    }
    console.error(`Error updating appointment status for ID ${appointmentId}:`, error);
    return { statusCode: 500, body: JSON.stringify({ message: 'Internal server error' }) };
  }
});