import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, readBody } from 'h3';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

const joinConsultationSchema = z.object({
  // No specific body parameters needed for joining, but we can add if required later
});

export default defineEventHandler(async (event) => {
  const appointmentId = event.context.params?.id;
  const body = await readBody(event); // Read body to trigger validation even if empty

  if (!appointmentId) {
    return { statusCode: 400, body: JSON.stringify({ message: 'Appointment ID is required' }) };
  }

  try {
    joinConsultationSchema.parse(body); // Validate empty body

    const appointment = await prisma.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        consultation: true,
      },
    });

    if (!appointment) {
      return { statusCode: 404, body: JSON.stringify({ message: 'Appointment not found' }) };
    }

    if (appointment.status !== 'SCHEDULED') {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: `Cannot join consultation. Appointment status is ${appointment.status}` }),
      };
    }

    let consultation;
    if (appointment.consultation) {
      consultation = appointment.consultation;
    } else {
      // In a real application, you would create a new consultation room/session here
      // For now, we'll simulate it
      consultation = await prisma.consultation.create({
        data: {
          appointmentId: appointment.id,
          roomUrl: `https://example.com/room/${appointment.id}`, // Placeholder URL
          status: 'IN_PROGRESS',
        },
      });
    }

    await prisma.appointment.update({
      where: { id: appointmentId },
      data: {
        status: 'IN_PROGRESS',
        updatedAt: new Date(),
      },
    });

    return { statusCode: 200, body: JSON.stringify({ message: 'Joined consultation successfully', consultation }) };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Validation error', errors: error.errors }),
      };
    }
    console.error(`Error joining consultation for ID ${appointmentId}:`, error);
    return { statusCode: 500, body: JSON.stringify({ message: 'Internal server error' }) };
  }
});