import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default defineEventHandler(async (event) => {
  const appointmentId = event.context.params?.id;

  if (!appointmentId) {
    return { statusCode: 400, body: JSON.stringify({ message: 'Appointment ID is required' }) };
  }

  try {
    const appointment = await prisma.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        provider: {
          include: {
            user: true,
          },
        },
        patient: {
          include: {
            user: true,
          },
        },
        consultation: true,
      },
    });

    if (!appointment) {
      return { statusCode: 404, body: JSON.stringify({ message: 'Appointment not found' }) };
    }

    return { statusCode: 200, body: JSON.stringify(appointment) };
  } catch (error) {
    console.error(`Error fetching appointment with ID ${appointmentId}:`, error);
    return { statusCode: 500, body: JSON.stringify({ message: 'Internal server error' }) };
  }
});