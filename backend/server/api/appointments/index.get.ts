import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, getQuery } from 'h3';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default defineEventHandler(async (event) => {
  const query = getQuery(event);
  const userId = query.userId as string | undefined;
  const role = query.role as string | undefined;

  try {
    let where: { provider?: { userId: string }; patient?: { userId: string } } = {};

    if (userId && role) {
      where = role === 'PROVIDER'
        ? { provider: { userId } }
        : { patient: { userId } };
    }

    const appointments = await prisma.appointment.findMany({
      where,
      include: {
        provider: {
          include: {
            user: true,
          },
        },
        patient: {
          include: {
            user: true,
          },
        },
        consultation: true,
      },
      orderBy: {
        appointmentDate: 'asc',
      },
    });

    return { statusCode: 200, body: JSON.stringify(appointments) };
  } catch (error) {
    console.error('Error fetching appointments:', error);
    return { statusCode: 500, body: JSON.stringify({ message: 'Internal server error' }) };
  }
});