import { defineEvent<PERSON>and<PERSON>, readBody } from 'h3';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

const createAppointmentSchema = z.object({
  providerId: z.string().min(1, 'Provider ID is required'),
  patientId: z.string().min(1, 'Patient ID is required'),
  appointmentDate: z.string().datetime('Invalid appointment date format'),
  reason: z.string().optional(),
  consultationType: z.enum(['VIDEO', 'AUDIO']),
});

export default defineEventHandler(async (event) => {
  const body = await readBody(event);

  try {
    const { providerId, patientId, appointmentDate, reason, consultationType } = createAppointmentSchema.parse(body);

    const appointment = await prisma.appointment.create({
      data: {
        providerId,
        patientId,
        appointmentDate: new Date(appointmentDate),
        reason: reason || '',
        status: 'SCHEDULED',
        consultationType,
      },
    });

    // TODO: Integrate notificationService.notifyAppointmentCreated(appointment.id);

    return { statusCode: 201, body: JSON.stringify(appointment) };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Validation error', errors: error.errors }),
      };
    }
    console.error('Error creating appointment:', error);
    return { statusCode: 500, body: JSON.stringify({ message: 'Internal server error' }) };
  }
});