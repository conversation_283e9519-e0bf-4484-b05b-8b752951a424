import { defineEventHandler } from 'h3';

export default defineEventHandler(async (event) => {
  return {
    success: true,
    message: 'Fintan Virtual Care Hub API is running 🚀',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth/*',
      users: '/api/users/*',
      patients: '/api/patients/*',
      appointments: '/api/appointments/*'
    }
  };
});
