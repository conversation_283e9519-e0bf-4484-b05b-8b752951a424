import { H3Event, H3Error } from 'h3';
import { registerUser, loginUser, getCurrentUser } from '../services/auth.service';
import { User } from '@prisma/client';

type UserRole = 'PATIENT' | 'PROVIDER' | 'ADMIN';

interface AuthUser extends Omit<User, 'password'> {
  role?: UserRole;
}

interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code: string;
    details?: unknown;
  };
}

interface RegisterInput {
  name: string;
  email: string;
  password: string;
  role?: UserRole;
}

interface LoginInput {
  email: string;
  password: string;
}

interface AuthResponse {
  user: Omit<AuthUser, 'password'>;
  accessToken: string;
  expiresIn: string;
}

export const register = async (event: H3Event): Promise<ApiResponse<{
  user: Omit<User, 'password'>;
  accessToken: string;
  expiresIn: string;
}>> => {
  try {
    const body = await readBody<RegisterInput>(event);
    
    // Validate input
    if (!body.name || !body.email || !body.password) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          details: 'Name, email, and password are required',
        },
      };
    }

    const result = await registerUser({
      name: body.name,
      email: body.email,
      password: body.password,
      role: body.role as 'PATIENT' | 'PROVIDER' | 'ADMIN' | undefined,
    });

    // Set HTTP-only cookie for refresh token
    setCookie(event, 'refreshToken', result.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7, // 7 days
      path: '/api/auth/refresh-token',
    });

    // Omit password from response
    const { password: _, ...userWithoutPassword } = result.user;

    return {
      success: true,
      data: {
        user: userWithoutPassword,
        accessToken: result.accessToken,
        expiresIn: result.expiresIn,
      },
    };
  } catch (error: unknown) {
    const err = error as H3Error;
    return {
      success: false,
      error: {
        code: err.statusMessage || 'REGISTRATION_FAILED',
        details: err.message || 'Failed to register user',
      },
    };
  }
};

export const login = async (event: H3Event) => {
  const body = await readBody(event);

  // Validate input
  if (!body.email || !body.password) {
    throw createError({
      statusCode: 400,
      statusMessage: 'ValidationError',
      message: 'Email and password are required',
    });
  }

  try {
    const result = await loginUser({
      email: body.email,
      password: body.password,
    });

    // Set HTTP-only cookie for refresh token
    setCookie(event, 'refreshToken', result.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7, // 7 days
      path: '/api/auth/refresh-token',
    });

    return {
      user: result.user,
      accessToken: result.accessToken,
      expiresIn: result.expiresIn,
    };
  } catch (error: unknown) {
    const err = error as { statusCode?: number; statusMessage?: string; message?: string };
    throw createError({
      statusCode: err.statusCode || 401,
      statusMessage: err.statusMessage || 'LoginFailed',
      message: err.message || 'Invalid credentials',
    });
  }
};

export const logout = async (event: H3Event) => {
  // Clear the refresh token cookie
  deleteCookie(event, 'refreshToken');
  
  return { success: true };
};

export const refreshToken = async (event: H3Event) => {
  const refreshToken = getCookie(event, 'refreshToken');
  
  if (!refreshToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
      message: 'No refresh token provided',
    });
  }

  try {
    // Here you would verify the refresh token and generate new tokens
    // This is a simplified example
    const result = {
      accessToken: 'new-access-token',
      refreshToken: 'new-refresh-token',
      expiresIn: '1h',
    };

    // Set new refresh token in cookie
    setCookie(event, 'refreshToken', result.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7, // 7 days
      path: '/api/auth/refresh-token',
    });

    return {
      accessToken: result.accessToken,
      expiresIn: result.expiresIn,
    };
  } catch (error: unknown) {
    const err = error as { statusCode?: number; statusMessage?: string; message?: string };
    throw createError({
      statusCode: err.statusCode || 401,
      statusMessage: err.statusMessage || 'InvalidToken',
      message: err.message || 'Invalid or expired refresh token',
    });
  }
};

export const me = async (event: H3Event) => {
  const user = event.context.user;
  
  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
      message: 'Not authenticated',
    });
  }

  try {
    return await getCurrentUser(user.id);
  } catch (error: unknown) {
    const err = error as { statusCode?: number; statusMessage?: string; message?: string };
    throw createError({
      statusCode: err.statusCode || 500,
      statusMessage: err.statusMessage || 'UserFetchFailed',
      message: err.message || 'Failed to fetch user',
    });
  }
};
