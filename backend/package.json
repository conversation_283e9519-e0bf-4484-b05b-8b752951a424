{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "prisma generate && nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma generate && prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:seed": "ts-node --esm prisma/seed.ts"}, "dependencies": {"@neondatabase/serverless": "^1.0.0", "@prisma/adapter-neon": "^6.9.0", "@prisma/client": "^6.9.0", "bcryptjs": "^2.4.3", "dotenv": "^16.4.5", "h3": "^1.12.0", "jsonwebtoken": "^9.0.2", "nuxt": "^3.17.5", "typescript": "^5.8.3", "zod": "^3.25.62"}, "devDependencies": {"@nuxt/test-utils": "^3.19.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "prisma": "^6.9.0", "ts-node": "^10.9.2", "vitest": "^3.2.3"}}