import axios from 'axios';

const API_BASE = 'http://localhost:3001/api';

async function testAuthEndpoints() {
  console.log('🧪 Testing Authentication Endpoints...\n');
  
  let testsPassed = 0;
  let testsTotal = 0;
  
  // Helper function to run tests
  const test = async (name, testFn) => {
    testsTotal++;
    try {
      await testFn();
      console.log(`✅ ${name}`);
      testsPassed++;
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Data:`, error.response.data);
      }
    }
  };
  
  // Test data
  const testUser = {
    name: 'Test User Auth',
    email: `test-auth-${Date.now()}@example.com`,
    password: 'testPassword123',
    role: 'PATIENT'
  };
  
  let accessToken = '';
  let refreshToken = '';
  
  // Test user registration
  await test('User registration', async () => {
    const response = await axios.post(`${API_BASE}/auth/register`, testUser);
    
    if (!response.data.success) {
      throw new Error('Registration response indicates failure');
    }
    
    if (!response.data.data.accessToken || !response.data.data.refreshToken) {
      throw new Error('Tokens not provided in registration response');
    }
    
    if (!response.data.data.user.id || response.data.data.user.email !== testUser.email) {
      throw new Error('User data not properly returned');
    }
    
    accessToken = response.data.data.accessToken;
    refreshToken = response.data.data.refreshToken;
  });
  
  // Test duplicate registration
  await test('Duplicate registration (should fail)', async () => {
    try {
      await axios.post(`${API_BASE}/auth/register`, testUser);
      throw new Error('Should have failed for duplicate email');
    } catch (error) {
      if (error.response && error.response.status === 409) {
        // Expected behavior
        return;
      }
      throw error;
    }
  });
  
  // Test login with correct credentials
  await test('Login with correct credentials', async () => {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    
    if (!response.data.success) {
      throw new Error('Login response indicates failure');
    }
    
    if (!response.data.data.accessToken || !response.data.data.refreshToken) {
      throw new Error('Tokens not provided in login response');
    }
    
    // Update tokens for further tests
    accessToken = response.data.data.accessToken;
    refreshToken = response.data.data.refreshToken;
  });
  
  // Test login with incorrect password
  await test('Login with incorrect password (should fail)', async () => {
    try {
      await axios.post(`${API_BASE}/auth/login`, {
        email: testUser.email,
        password: 'wrongPassword'
      });
      throw new Error('Should have failed for incorrect password');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        // Expected behavior
        return;
      }
      throw error;
    }
  });
  
  // Test login with non-existent email
  await test('Login with non-existent email (should fail)', async () => {
    try {
      await axios.post(`${API_BASE}/auth/login`, {
        email: '<EMAIL>',
        password: 'anyPassword'
      });
      throw new Error('Should have failed for non-existent email');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        // Expected behavior
        return;
      }
      throw error;
    }
  });
  
  // Test protected route without token
  await test('Protected route without token (should fail)', async () => {
    try {
      await axios.get(`${API_BASE}/users/me`);
      throw new Error('Should have failed without token');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        // Expected behavior
        return;
      }
      throw error;
    }
  });
  
  // Test protected route with valid token
  await test('Protected route with valid token', async () => {
    const response = await axios.get(`${API_BASE}/users/me`, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });
    
    if (response.status !== 200) {
      throw new Error('Protected route should return 200 with valid token');
    }
  });
  
  // Test protected route with invalid token
  await test('Protected route with invalid token (should fail)', async () => {
    try {
      await axios.get(`${API_BASE}/users/me`, {
        headers: {
          Authorization: 'Bearer invalid.token.here'
        }
      });
      throw new Error('Should have failed with invalid token');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        // Expected behavior
        return;
      }
      throw error;
    }
  });
  
  // Test validation errors
  await test('Registration validation (invalid email)', async () => {
    try {
      await axios.post(`${API_BASE}/auth/register`, {
        name: 'Test',
        email: 'invalid-email',
        password: 'password123',
        role: 'PATIENT'
      });
      throw new Error('Should have failed for invalid email');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        // Expected behavior
        return;
      }
      throw error;
    }
  });
  
  await test('Registration validation (short password)', async () => {
    try {
      await axios.post(`${API_BASE}/auth/register`, {
        name: 'Test',
        email: '<EMAIL>',
        password: '123',
        role: 'PATIENT'
      });
      throw new Error('Should have failed for short password');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        // Expected behavior
        return;
      }
      throw error;
    }
  });
  
  // Summary
  console.log(`\n📊 Test Results: ${testsPassed}/${testsTotal} tests passed`);
  
  if (testsPassed === testsTotal) {
    console.log('🎉 All authentication endpoint tests passed!');
  } else {
    console.log('❌ Some tests failed. Please check the implementation.');
    process.exit(1);
  }
}

testAuthEndpoints().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});
