import { 
  hashPassword, 
  comparePassword, 
  generateToken, 
  generateRefreshToken,
  verifyToken, 
  verifyRefreshToken,
  extractTokenFromHeader,
  generateSecureRandomString,
  createUserPayload,
  AUTH_CONSTANTS
} from '../backend/server/utils/auth.ts';

async function testAuthUtils() {
  console.log('🧪 Testing Authentication Utilities...\n');
  
  let testsPassed = 0;
  let testsTotal = 0;
  
  // Helper function to run tests
  const test = async (name, testFn) => {
    testsTotal++;
    try {
      await testFn();
      console.log(`✅ ${name}`);
      testsPassed++;
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
    }
  };
  
  // Test password hashing
  await test('Password hashing', async () => {
    const password = 'testPassword123';
    const hash = await hashPassword(password);
    
    if (!hash || hash === password) {
      throw new Error('Password was not hashed properly');
    }
    
    if (hash.length < 50) {
      throw new Error('Hash seems too short');
    }
  });
  
  // Test password comparison - valid password
  await test('Password comparison (valid)', async () => {
    const password = 'testPassword123';
    const hash = await hashPassword(password);
    const isValid = await comparePassword(password, hash);
    
    if (!isValid) {
      throw new Error('Valid password comparison failed');
    }
  });
  
  // Test password comparison - invalid password
  await test('Password comparison (invalid)', async () => {
    const password = 'testPassword123';
    const wrongPassword = 'wrongPassword456';
    const hash = await hashPassword(password);
    const isValid = await comparePassword(wrongPassword, hash);
    
    if (isValid) {
      throw new Error('Invalid password should not match');
    }
  });
  
  // Test password validation
  await test('Password validation (empty)', async () => {
    try {
      await hashPassword('');
      throw new Error('Should have thrown error for empty password');
    } catch (error) {
      if (!error.message.includes('non-empty string')) {
        throw error;
      }
    }
  });
  
  await test('Password validation (too short)', async () => {
    try {
      await hashPassword('123');
      throw new Error('Should have thrown error for short password');
    } catch (error) {
      if (!error.message.includes('at least 6 characters')) {
        throw error;
      }
    }
  });
  
  // Test JWT token generation
  await test('JWT token generation', async () => {
    const payload = { userId: '123', email: '<EMAIL>', role: 'PATIENT' };
    const token = generateToken(payload);
    
    if (!token || typeof token !== 'string') {
      throw new Error('Token was not generated properly');
    }
    
    if (token.split('.').length !== 3) {
      throw new Error('Token does not have proper JWT structure');
    }
  });
  
  // Test JWT token verification
  await test('JWT token verification (valid)', async () => {
    const payload = { userId: '123', email: '<EMAIL>', role: 'PATIENT' };
    const token = generateToken(payload);
    const decoded = verifyToken(token);
    
    if (decoded.userId !== payload.userId || decoded.email !== payload.email) {
      throw new Error('Decoded token does not match original payload');
    }
  });
  
  // Test JWT token verification - invalid token
  await test('JWT token verification (invalid)', async () => {
    try {
      verifyToken('invalid.token.here');
      throw new Error('Should have thrown error for invalid token');
    } catch (error) {
      if (!error.message.includes('Invalid token')) {
        throw error;
      }
    }
  });
  
  // Test refresh token generation and verification
  await test('Refresh token generation and verification', async () => {
    const payload = { userId: '123', email: '<EMAIL>' };
    const refreshToken = generateRefreshToken(payload);
    const decoded = verifyRefreshToken(refreshToken);
    
    if (decoded.userId !== payload.userId) {
      throw new Error('Refresh token verification failed');
    }
  });
  
  // Test token extraction from header
  await test('Token extraction from header', async () => {
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9';
    const authHeader = `Bearer ${token}`;
    const extracted = extractTokenFromHeader(authHeader);
    
    if (extracted !== token) {
      throw new Error('Token extraction failed');
    }
  });
  
  await test('Token extraction from invalid header', async () => {
    const extracted1 = extractTokenFromHeader('InvalidHeader');
    const extracted2 = extractTokenFromHeader(undefined);
    
    if (extracted1 !== null || extracted2 !== null) {
      throw new Error('Should return null for invalid headers');
    }
  });
  
  // Test secure random string generation
  await test('Secure random string generation', async () => {
    const str1 = generateSecureRandomString(32);
    const str2 = generateSecureRandomString(32);
    
    if (str1.length !== 32 || str2.length !== 32) {
      throw new Error('Random string length is incorrect');
    }
    
    if (str1 === str2) {
      throw new Error('Random strings should be different');
    }
  });
  
  // Test user payload creation
  await test('User payload creation', async () => {
    const user = {
      id: '123',
      email: '<EMAIL>',
      role: 'PATIENT',
      name: 'Test User'
    };
    
    const payload = createUserPayload(user);
    
    if (payload.userId !== user.id || payload.email !== user.email) {
      throw new Error('User payload creation failed');
    }
    
    if (!payload.iat || typeof payload.iat !== 'number') {
      throw new Error('Issued at time not set properly');
    }
  });
  
  // Test constants
  await test('Auth constants', async () => {
    if (!AUTH_CONSTANTS.JWT_SECRET || !AUTH_CONSTANTS.SALT_ROUNDS) {
      throw new Error('Auth constants not properly exported');
    }
    
    if (AUTH_CONSTANTS.SALT_ROUNDS !== 12) {
      throw new Error('Salt rounds should be 12');
    }
  });
  
  // Summary
  console.log(`\n📊 Test Results: ${testsPassed}/${testsTotal} tests passed`);
  
  if (testsPassed === testsTotal) {
    console.log('🎉 All authentication utility tests passed!');
  } else {
    console.log('❌ Some tests failed. Please check the implementation.');
    process.exit(1);
  }
}

testAuthUtils().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});
