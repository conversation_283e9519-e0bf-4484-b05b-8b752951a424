import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function main() {
  console.log('🧪 Testing Neon PostgreSQL connection...');

  // Check for required environment variables
  if (!process.env.DATABASE_URL) {
    console.error('❌ Error: DATABASE_URL environment variable is required');
    console.error('Please set this variable in your .env file');
    process.exit(1);
  }

  const databaseUrl = process.env.DATABASE_URL;
  console.log('📊 Connection string format:');
  console.log(`DATABASE_URL: ${databaseUrl.replace(/:[^:]*@/, ':****@')}`);

  try {
    // Create simple Prisma client (recommended for most applications)
    console.log('🔄 Initializing Prisma client...');
    const prisma = new PrismaClient({
      log: ['query', 'error', 'warn'],
    });

    // Test connection
    console.log('🔄 Testing database connection...');
    await prisma.$connect();
    console.log('✅ Successfully connected to Neon database');

    // Get database information
    console.log('🔄 Fetching database information...');
    const result = await prisma.$queryRaw`SELECT current_database(), current_schema(), version()`;
    console.log('📊 Database information:');
    console.log(result);

    // Test query performance
    console.log('🔄 Testing query performance...');
    const startTime = Date.now();
    await prisma.$queryRaw`SELECT 1 as test`;
    const endTime = Date.now();
    console.log(`✅ Query completed in ${endTime - startTime}ms`);

    // Test if we can access tables (this will show if migrations are needed)
    console.log('🔄 Testing table access...');
    try {
      const userCount = await prisma.user.count();
      console.log(`📊 Found ${userCount} users in database`);
    } catch (tableError) {
      console.log('⚠️ Tables not found - migrations may be needed');
      console.log('Run: npx prisma migrate dev --name init');
    }

    // Clean up
    console.log('🔄 Closing connections...');
    await prisma.$disconnect();

    console.log('✅ Neon database connection test completed successfully');
  } catch (error) {
    console.error('❌ Error testing Neon database connection:', error);
    process.exit(1);
  }
}

main();

