import axios from 'axios';

const API_BASE = 'http://localhost:3001/api';

async function testAPI() {
  console.log('🧪 Testing Backend API...');
  
  try {
    // Test health endpoint
    console.log('🔄 Testing health endpoint...');
    const healthResponse = await axios.get(`${API_BASE}/health`);
    console.log('✅ Health endpoint response:', healthResponse.data);
    
    // Test CORS headers
    console.log('🔄 Testing CORS headers...');
    const corsHeaders = healthResponse.headers;
    console.log('📊 Response headers:');
    console.log('  Access-Control-Allow-Origin:', corsHeaders['access-control-allow-origin']);
    console.log('  Access-Control-Allow-Methods:', corsHeaders['access-control-allow-methods']);
    console.log('  Access-Control-Allow-Headers:', corsHeaders['access-control-allow-headers']);
    
    // Test OPTIONS request (preflight)
    console.log('🔄 Testing OPTIONS preflight request...');
    const optionsResponse = await axios.options(`${API_BASE}/health`);
    console.log('✅ OPTIONS request successful');

    // Test error handling
    console.log('🔄 Testing error handling...');
    try {
      await axios.get(`${API_BASE}/test-error`);
      console.log('❌ Error endpoint should have thrown an error');
    } catch (errorResponse) {
      if (errorResponse.response && errorResponse.response.status === 400) {
        console.log('✅ Error handling working correctly');
        console.log('📊 Error response:', errorResponse.response.data);
      } else {
        throw errorResponse;
      }
    }

    console.log('🎉 All API tests passed!');
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    process.exit(1);
  }
}

testAPI();
