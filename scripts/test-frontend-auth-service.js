import axios from 'axios';

// Simulate the frontend authService
const API_URL = 'http://localhost:3001/api/auth/';

// Types for API responses (simulating TypeScript interfaces)
const authService = {
  async register(name, email, password, role = 'PATIENT') {
    try {
      const response = await axios.post(API_URL + 'register', {
        name,
        email,
        password,
        role,
      });
      
      if (response.data.success && response.data.data) {
        // Store user data and tokens (simulating localStorage)
        const userData = {
          user: JSON.stringify(response.data.data.user),
          accessToken: response.data.data.accessToken,
          refreshToken: response.data.data.refreshToken
        };
        
        console.log('📦 Stored user data:', {
          user: response.data.data.user.email,
          role: response.data.data.user.role,
          hasToken: !!response.data.data.accessToken
        });
        
        return response.data;
      } else {
        throw new Error('Registration failed');
      }
    } catch (error) {
      if (error.response?.data) {
        throw new Error(error.response.data.message || error.response.data.error?.message || 'Registration failed');
      }
      throw error;
    }
  },

  async login(email, password) {
    try {
      const response = await axios.post(API_URL + 'login', {
        email,
        password,
      });
      
      if (response.data.success && response.data.data) {
        // Store user data and tokens (simulating localStorage)
        const userData = {
          user: JSON.stringify(response.data.data.user),
          accessToken: response.data.data.accessToken,
          refreshToken: response.data.data.refreshToken
        };
        
        console.log('📦 Stored user data:', {
          user: response.data.data.user.email,
          role: response.data.data.user.role,
          hasToken: !!response.data.data.accessToken
        });
        
        return response.data;
      } else {
        throw new Error('Login failed');
      }
    } catch (error) {
      if (error.response?.data) {
        throw new Error(error.response.data.message || error.response.data.error?.message || 'Login failed');
      }
      throw error;
    }
  },

  async getCurrentUser(token) {
    try {
      const response = await axios.get('http://localhost:3001/api/users/me', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      if (response.data.success && response.data.user) {
        return response.data.user;
      } else {
        throw new Error('Failed to get current user');
      }
    } catch (error) {
      if (error.response?.status === 401) {
        throw new Error('Token expired or invalid');
      }
      throw error;
    }
  }
};

async function testFrontendAuthService() {
  console.log('🧪 Testing Frontend AuthService Integration...\n');
  
  let testsPassed = 0;
  let testsTotal = 0;
  
  // Helper function to run tests
  const test = async (name, testFn) => {
    testsTotal++;
    try {
      await testFn();
      console.log(`✅ ${name}`);
      testsPassed++;
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
    }
  };
  
  // Test data
  const testUser = {
    name: 'Frontend Test User',
    email: `frontend-test-${Date.now()}@example.com`,
    password: 'frontendTest123',
    role: 'PATIENT'
  };
  
  let authResponse = null;
  let accessToken = '';
  
  // Test 1: Frontend Registration Flow
  await test('Frontend Registration Flow', async () => {
    authResponse = await authService.register(
      testUser.name, 
      testUser.email, 
      testUser.password, 
      testUser.role
    );
    
    if (!authResponse.success || !authResponse.data.user) {
      throw new Error('Registration response invalid');
    }
    
    accessToken = authResponse.data.accessToken;
    
    if (!accessToken) {
      throw new Error('Access token not received');
    }
  });
  
  // Test 2: Frontend Login Flow
  await test('Frontend Login Flow', async () => {
    const loginResponse = await authService.login(testUser.email, testUser.password);
    
    if (!loginResponse.success || !loginResponse.data.user) {
      throw new Error('Login response invalid');
    }
    
    accessToken = loginResponse.data.accessToken;
    
    if (loginResponse.data.user.email !== testUser.email) {
      throw new Error('User email mismatch');
    }
  });
  
  // Test 3: Get Current User
  await test('Get Current User', async () => {
    const user = await authService.getCurrentUser(accessToken);
    
    if (!user || user.email !== testUser.email) {
      throw new Error('Current user data invalid');
    }
    
    if (user.role !== testUser.role) {
      throw new Error('User role mismatch');
    }
  });
  
  // Test 4: Provider Registration
  await test('Provider Registration', async () => {
    const providerUser = {
      name: 'Dr. Frontend Provider',
      email: `frontend-provider-${Date.now()}@example.com`,
      password: 'providerTest123',
      role: 'PROVIDER'
    };
    
    const providerResponse = await authService.register(
      providerUser.name,
      providerUser.email,
      providerUser.password,
      providerUser.role
    );
    
    if (providerResponse.data.user.role !== 'PROVIDER') {
      throw new Error('Provider role not set correctly');
    }
    
    if (!providerResponse.data.user.provider) {
      throw new Error('Provider record not created');
    }
  });
  
  // Test 5: Error Handling - Invalid Credentials
  await test('Error Handling - Invalid Credentials', async () => {
    try {
      await authService.login('<EMAIL>', 'wrongpassword');
      throw new Error('Should have failed with invalid credentials');
    } catch (error) {
      if (!error.message.includes('Invalid credentials')) {
        throw new Error('Expected invalid credentials error');
      }
    }
  });
  
  // Test 6: Error Handling - Duplicate Registration
  await test('Error Handling - Duplicate Registration', async () => {
    try {
      await authService.register(
        testUser.name,
        testUser.email, // Same email as before
        testUser.password,
        testUser.role
      );
      throw new Error('Should have failed with duplicate email');
    } catch (error) {
      if (!error.message.includes('already exists')) {
        throw new Error('Expected duplicate email error');
      }
    }
  });
  
  // Test 7: Error Handling - Invalid Token
  await test('Error Handling - Invalid Token', async () => {
    try {
      await authService.getCurrentUser('invalid.token.here');
      throw new Error('Should have failed with invalid token');
    } catch (error) {
      if (!error.message.includes('Token expired or invalid')) {
        throw new Error('Expected token error');
      }
    }
  });
  
  // Summary
  console.log(`\n📊 Test Results: ${testsPassed}/${testsTotal} tests passed`);
  
  if (testsPassed === testsTotal) {
    console.log('🎉 All frontend auth service tests passed!');
    console.log('\n✅ Frontend-Backend Integration Status:');
    console.log('   - Registration form can connect to backend');
    console.log('   - Login form can connect to backend');
    console.log('   - Token storage and retrieval working');
    console.log('   - Protected route access working');
    console.log('   - Error handling working correctly');
    console.log('   - Role-based registration working');
  } else {
    console.log('❌ Some tests failed. Please check the implementation.');
    process.exit(1);
  }
}

testFrontendAuthService().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});
