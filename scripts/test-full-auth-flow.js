import axios from 'axios';

const API_BASE = 'http://localhost:3001/api';

async function testFullAuthFlow() {
  console.log('🧪 Testing Full Authentication Flow...\n');
  
  let testsPassed = 0;
  let testsTotal = 0;
  
  // Helper function to run tests
  const test = async (name, testFn) => {
    testsTotal++;
    try {
      await testFn();
      console.log(`✅ ${name}`);
      testsPassed++;
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Data:`, error.response.data);
      }
    }
  };
  
  // Test data
  const testUser = {
    name: 'Full Auth Test User',
    email: `full-auth-test-${Date.now()}@example.com`,
    password: 'testPassword123',
    role: 'PATIENT'
  };
  
  let accessToken = '';
  let userData = null;
  
  // Test 1: User Registration
  await test('User Registration', async () => {
    const response = await axios.post(`${API_BASE}/auth/register`, testUser);
    
    if (!response.data.success) {
      throw new Error('Registration response indicates failure');
    }
    
    if (!response.data.data.accessToken || !response.data.data.user) {
      throw new Error('Registration response missing required data');
    }
    
    accessToken = response.data.data.accessToken;
    userData = response.data.data.user;
    
    if (userData.email !== testUser.email || userData.role !== testUser.role) {
      throw new Error('User data mismatch in registration response');
    }
  });
  
  // Test 2: Access Protected Route with Token
  await test('Access Protected Route (/users/me)', async () => {
    const response = await axios.get(`${API_BASE}/users/me`, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });
    
    if (!response.data.success || !response.data.user) {
      throw new Error('Protected route response invalid');
    }
    
    if (response.data.user.id !== userData.id) {
      throw new Error('User ID mismatch in protected route');
    }
  });
  
  // Test 3: Logout and Login Flow
  await test('Login with Registered User', async () => {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    
    if (!response.data.success) {
      throw new Error('Login response indicates failure');
    }
    
    if (!response.data.data.accessToken || !response.data.data.user) {
      throw new Error('Login response missing required data');
    }
    
    // Update token for further tests
    accessToken = response.data.data.accessToken;
    
    if (response.data.data.user.email !== testUser.email) {
      throw new Error('User email mismatch in login response');
    }
  });
  
  // Test 4: Token Validation After Login
  await test('Token Validation After Login', async () => {
    const response = await axios.get(`${API_BASE}/users/me`, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });
    
    if (!response.data.success || !response.data.user) {
      throw new Error('Token validation failed after login');
    }
  });
  
  // Test 5: Invalid Token Handling
  await test('Invalid Token Handling', async () => {
    try {
      await axios.get(`${API_BASE}/users/me`, {
        headers: {
          Authorization: 'Bearer invalid.token.here'
        }
      });
      throw new Error('Should have failed with invalid token');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        // Expected behavior
        return;
      }
      throw error;
    }
  });
  
  // Test 6: No Token Handling
  await test('No Token Handling', async () => {
    try {
      await axios.get(`${API_BASE}/users/me`);
      throw new Error('Should have failed without token');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        // Expected behavior
        return;
      }
      throw error;
    }
  });
  
  // Test 7: Provider Registration
  await test('Provider Registration', async () => {
    const providerUser = {
      name: 'Dr. Test Provider',
      email: `provider-test-${Date.now()}@example.com`,
      password: 'providerPassword123',
      role: 'PROVIDER'
    };
    
    const response = await axios.post(`${API_BASE}/auth/register`, providerUser);
    
    if (!response.data.success) {
      throw new Error('Provider registration failed');
    }
    
    if (response.data.data.user.role !== 'PROVIDER') {
      throw new Error('Provider role not set correctly');
    }
    
    if (!response.data.data.user.provider) {
      throw new Error('Provider record not created');
    }
  });
  
  // Test 8: Patient Record Creation
  await test('Patient Record Verification', async () => {
    // Use the original patient user token
    const response = await axios.get(`${API_BASE}/users/me`, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });
    
    if (!response.data.user.patient) {
      throw new Error('Patient record not created for PATIENT role');
    }
    
    if (response.data.user.role !== 'PATIENT') {
      throw new Error('User role not set correctly');
    }
  });
  
  // Test 9: CORS Headers
  await test('CORS Headers Present', async () => {
    const response = await axios.get(`${API_BASE}/health`);
    
    const corsHeaders = response.headers;
    if (!corsHeaders['access-control-allow-origin']) {
      throw new Error('CORS headers not present');
    }
  });
  
  // Test 10: API Health Check
  await test('API Health Check', async () => {
    const response = await axios.get(`${API_BASE}/health`);
    
    if (!response.data.success) {
      throw new Error('Health check failed');
    }
    
    if (!response.data.endpoints) {
      throw new Error('API endpoints not listed in health check');
    }
  });
  
  // Summary
  console.log(`\n📊 Test Results: ${testsPassed}/${testsTotal} tests passed`);
  
  if (testsPassed === testsTotal) {
    console.log('🎉 All full authentication flow tests passed!');
    console.log('\n✅ Authentication System Status:');
    console.log('   - User registration working');
    console.log('   - User login working');
    console.log('   - Token generation and validation working');
    console.log('   - Protected routes working');
    console.log('   - Role-based user creation working');
    console.log('   - CORS configuration working');
    console.log('   - Error handling working');
  } else {
    console.log('❌ Some tests failed. Please check the implementation.');
    process.exit(1);
  }
}

testFullAuthFlow().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});
